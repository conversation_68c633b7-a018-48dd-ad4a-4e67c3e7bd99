"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.LanguageFactory = void 0;
const ptDefault = __importStar(require("../Language/pt"));
const i18next_1 = __importDefault(require("i18next"));
const i18next_browser_languagedetector_1 = __importDefault(require("i18next-browser-languagedetector"));
const Debug_1 = require("@thrift/common/engines/Debug");
const LanguageFactory = ({ fallbackLng, packages, }) => {
    const { pt: ptExternal } = packages || {};
    const pt = ptExternal ? Object.assign(Object.assign({}, ptExternal), ptDefault) : ptDefault;
    i18next_1.default.use(i18next_browser_languagedetector_1.default).init({
        debug: false,
        lowerCaseLng: true,
        fallbackLng: fallbackLng,
        resources: { pt },
        interpolation: { escapeValue: false },
        defaultNS: 'common',
        ns: Object.keys(pt),
    }, (err) => err
        ? Debug_1.Debug.error(err)
        : Debug_1.Debug.info({
            message: `Locale has already loaded successfully (${i18next_1.default.language})`,
        }));
    return { t: i18next_1.default.t.bind(i18next_1.default) };
};
exports.LanguageFactory = LanguageFactory;
//# sourceMappingURL=i18next.js.map