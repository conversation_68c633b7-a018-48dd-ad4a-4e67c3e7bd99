import type { LanguageFactoryProps } from '@thrift/i18n/engines/Language'
import * as ptDefault from '@thrift/i18n/engines/Language/pt'
import i18next from 'i18next'
import LanguageDetector from 'i18next-browser-languagedetector'

import { Debug } from '@thrift/common/engines/Debug'

export const LanguageFactory = ({
  fallbackLng,
  packages,
}: LanguageFactoryProps) => {
  const { pt: ptExternal } = packages || {}
  const pt = ptExternal ? { ...ptExternal, ...ptDefault } : ptDefault

  i18next.use(LanguageDetector).init(
    {
      debug: false,
      lowerCaseLng: true,
      fallbackLng: fallbackLng,
      resources: { pt },
      interpolation: { escapeValue: false },
      defaultNS: 'common',
      ns: Object.keys(pt),
    },
    (err) =>
      err
        ? Debug.error(err)
        : Debug.info({
            message: `Locale has already loaded successfully (${i18next.language})`,
          }),
  )

  return { t: i18next.t.bind(i18next) }
}
