{"name": "@thrift/i18n", "version": "0.1.0", "private": true, "description": "i18n - Internationalization and Localization", "author": "Thrift Technology <<EMAIL>>", "type": "commonjs", "repository": {"type": "git", "url": "https://github.com/thrift-technology/i18n.git"}, "scripts": {"build": "yarn healthcheck && tsc --project tsconfig.json && tsc-alias -p tsconfig.json", "format": "prettier --check .", "format:fix": "prettier --write .", "healthcheck": "yarn lint && yarn format && yarn typecheck", "lint": "eslint ./src --ext .ts", "lint:fix": "yarn lint --fix", "prepare": "husky", "typecheck": "tsc --noEmit"}, "dependencies": {"@thrift/common": "https://github.com/thrift-technology/ms-common.git#main", "i18next": "25.3.2", "i18next-browser-languagedetector": "8.2.0", "react": "19.1.1", "react-dom": "19.1.1", "react-router": "7.7.1"}, "devDependencies": {"@eslint/js": "9.32.0", "@trivago/prettier-plugin-sort-imports": "5.2.2", "@tsconfig/recommended": "1.0.10", "@types/express": "5.0.3", "@types/node": "24.1.0", "axios": "1.11.0", "dotenv": "17.2.1", "eslint-config-prettier": "10.1.8", "eslint-plugin-prettier": "5.5.3", "eslint-plugin-unused-imports": "4.1.4", "eslint": "9.32.0", "express": "5.1.0", "husky": "^9.1.7", "prettier": "3.6.2", "tsc-alias": "1.8.16", "typescript-eslint": "8.38.0", "typescript": "5.8.3"}, "engines": {"node": ">=20 <25", "npm": ">=10 <12", "yarn": ">=1.22 <2"}}