{"info": {"_postman_id": "838766-f6a02f66-a18f-479b-9cda-d50bbe115b32", "name": "Account", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "item": [{"name": "Authentication", "item": [{"name": "authenticate", "id": "838766-e33d292b-f627-44be-a878-09c72c9fe693", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "OPTIONS", "header": [], "url": {"raw": "{{baseUrl}}/authenticate", "host": ["{{baseUrl}}"], "path": ["authenticate"]}}, "response": []}, {"name": "authenticate", "event": [{"listen": "test", "script": {"id": "3fd85455-adce-477c-adf1-dd73b3f47e01", "exec": ["const response = pm.response.json();", "if (pm.response.code === 201) {", "    const authHeader = pm.response.headers.get('Authorization');", "    const token = authHeader ? authHeader.replace('Bearer ', '') : null;", "    pm.environment.set('token', token);", "    pm.environment.set(\"refreshToken\", pm.response.headers.get('X-Refresh-Token'));", "    console.log(\"Authorized!\")", "} else {", "    console.log(\"Authorization failed: \" + response.message);", "}"], "type": "text/javascript", "packages": {}}}], "id": "838766-7214e57e-2d30-4bce-93ee-a32574b68660", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"auth": {"type": "<PERSON><PERSON><PERSON>"}, "method": "POST", "header": [{"key": "Origin", "value": "http://{{tenant}}.thrift.technology", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"tenantId\": \"c597c17c-b077-4fd7-a3b1-516b2541dceb\",\n    \"email\": \"{{email}}\",\n    \"password\": \"{{password}}\"\n}\n", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{baseUrl}}/authenticate", "host": ["{{baseUrl}}"], "path": ["authenticate"]}}, "response": []}, {"name": "authenticate/refresh", "id": "838766-d52f7ef0-4a36-4cb1-80c2-7c1183c4e3d4", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "OPTIONS", "header": [], "url": {"raw": "{{baseUrl}}/authenticate/refresh", "host": ["{{baseUrl}}"], "path": ["authenticate", "refresh"]}}, "response": []}, {"name": "authenticate refresh", "event": [{"listen": "test", "script": {"id": "46ab3b76-8811-4991-9d7e-6f8e5b5570a5", "exec": ["if (pm.response.code === 201) {", "    const authHeader = pm.response.headers.get('Authorization');", "    const token = authHeader ? authHeader.replace('Bearer ', '') : null;", "    pm.environment.set('token', token);", "    pm.environment.set(\"refreshToken\", pm.response.headers.get('X-Refresh-Token'));", "    console.log(\"Authorization refreshed!\")", "} else {", "    console.log(\"Authorization refresh failed: \" + response.message);", "}"], "type": "text/javascript", "packages": {}}}], "id": "838766-a018ca41-1aa8-4679-b417-55916033d356", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{token}}", "type": "string"}]}, "method": "POST", "header": [{"key": "X-Refresh-Token", "value": "{{refreshToken}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"refreshToken\": \"{{refreshToken}}\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{baseUrl}}/authenticate/refresh", "host": ["{{baseUrl}}"], "path": ["authenticate", "refresh"]}}, "response": []}], "id": "838766-84a31318-f90f-4d6c-8453-c9cdca20f1a2", "event": [{"listen": "prerequest", "script": {"type": "text/javascript", "packages": {}, "exec": [""], "id": "a1672b39-b039-40ce-943b-76f3e44a3c17"}}, {"listen": "test", "script": {"type": "text/javascript", "packages": {}, "exec": [""], "id": "b749e04f-e6fa-4c78-a790-9d6431d1fa01"}}]}, {"name": "Role", "item": [{"name": "roles", "id": "838766-6fafc389-7dec-4701-8410-d1b6090d5d79", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "OPTIONS", "header": [], "url": {"raw": "{{baseUrl}}/roles", "host": ["{{baseUrl}}"], "path": ["roles"]}}, "response": []}, {"name": "roles", "event": [{"listen": "test", "script": {"id": "afddaa88-2219-441a-8c2d-c838e875642d", "exec": [""], "type": "text/javascript", "packages": {}}}], "id": "838766-ba34f8d8-7aa8-40b2-bbb8-094d9ba503d9", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "GET", "header": [{"key": "X-Refresh-Token", "value": "{{refreshToken}}", "type": "text"}], "url": {"raw": "{{baseUrl}}/roles", "host": ["{{baseUrl}}"], "path": ["roles"]}}, "response": []}, {"name": "roles/:id", "id": "838766-eec798e4-ca7e-47ff-a7cf-f82ef5c1aa29", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "OPTIONS", "header": [], "url": {"raw": "{{baseUrl}}/roles/:id", "host": ["{{baseUrl}}"], "path": ["roles", ":id"], "variable": [{"id": "4a5bedaf-063b-433b-b86f-6e9d09bebc83", "key": "id", "value": "16799c36-4002-47a0-b72a-43bfbbe384e0"}]}}, "response": []}, {"name": "roles/:id", "event": [{"listen": "test", "script": {"id": "8d5f26e5-e2b8-429b-919f-d2bc247ed65d", "exec": [""], "type": "text/javascript", "packages": {}}}], "id": "838766-eb68f933-1b21-4457-a662-09bf7f926703", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "GET", "header": [{"key": "X-Refresh-Token", "value": "{{refreshToken}}", "type": "text"}], "url": {"raw": "{{baseUrl}}/roles/:id", "host": ["{{baseUrl}}"], "path": ["roles", ":id"], "variable": [{"key": "id", "value": "c8d26cbc-deeb-4a18-bcdf-5bf9334dc19e"}]}}, "response": []}, {"name": "roles", "event": [{"listen": "test", "script": {"exec": [""], "type": "text/javascript", "packages": {}, "id": "348e3a1f-900d-44f0-8f62-f90cfaa81a14"}}], "id": "838766-9097eb4f-f93e-4b78-aebd-b2501d9bad2e", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\n    \"shortname\": \"testiang\",\n    \"description\": \"role x\",\n    \"name\": \"heyhey\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{baseUrl}}/roles", "host": ["{{baseUrl}}"], "path": ["roles"]}}, "response": []}, {"name": "roles/:id", "event": [{"listen": "test", "script": {"exec": [""], "type": "text/javascript", "packages": {}, "id": "ac4707cc-ebf4-4be8-b048-9fe9cd7a4c4f"}}], "id": "838766-43e8f95d-0aa7-4ae8-9be4-fd63b2127ef2", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "PUT", "header": [], "body": {"mode": "raw", "raw": "{\n    \"shortname\": \"testaang\",\n    \"description\": \"role x\",\n    \"name\": \"heyhey\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{baseUrl}}/roles/:id", "host": ["{{baseUrl}}"], "path": ["roles", ":id"], "variable": [{"id": "f9ccc0f7-012b-4556-b060-863e8128332f", "key": "id", "value": ""}]}}, "response": []}, {"name": "roles/:id", "event": [{"listen": "test", "script": {"exec": [""], "type": "text/javascript", "packages": {}, "id": "f63fe7fc-6725-405a-9629-27ebdc320802"}}], "id": "838766-abe33ae2-d4ac-4d1f-b718-162273f06027", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "DELETE", "header": [], "url": {"raw": "{{baseUrl}}/roles/:id", "host": ["{{baseUrl}}"], "path": ["roles", ":id"], "variable": [{"id": "76777008-6e8b-4081-8182-3ea8a3d216b2", "key": "id", "value": ""}]}}, "response": []}, {"name": "roles/:id/acls", "event": [{"listen": "test", "script": {"exec": [""], "type": "text/javascript", "packages": {}, "id": "44eaab7d-af59-4df4-b6ce-f74b76e20bd0"}}], "id": "838766-6c24142f-213b-4143-9cc3-68359e2eac58", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\n    \"aclIds\": []\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{baseUrl}}/roles/:id/acls", "host": ["{{baseUrl}}"], "path": ["roles", ":id", "acls"], "variable": [{"id": "bee15dc4-5711-4bf5-892f-a822d018f009", "key": "id", "value": "c8d26cbc-deeb-4a18-bcdf-5bf9334dc19e"}]}}, "response": []}], "id": "838766-454d0c4f-de5e-411a-a568-5a38fd035976", "event": [{"listen": "prerequest", "script": {"type": "text/javascript", "packages": {}, "exec": [""], "id": "3be03e3b-f9c2-4922-a662-7b60ae89202c"}}, {"listen": "test", "script": {"type": "text/javascript", "packages": {}, "exec": [""], "id": "99b60935-6477-4b7d-aa0e-5a90c83699ec"}}]}, {"name": "Acl", "item": [{"name": "acl", "event": [{"listen": "test", "script": {"exec": [""], "type": "text/javascript", "packages": {}, "id": "c8dc0dff-2427-41ec-ac46-8cd412b3799a"}}], "id": "838766-8a75daa4-b387-4fe1-b4b8-07f61c2517f8", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/acl", "host": ["{{baseUrl}}"], "path": ["acl"]}}, "response": []}, {"name": "acl", "event": [{"listen": "test", "script": {"exec": [""], "type": "text/javascript", "packages": {}, "id": "2cae6e2b-b6c7-4f8a-89f1-67b358c6c873"}}], "id": "838766-81e0c21b-0a35-4e9c-a832-67cfa4a7b259", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "OPTIONS", "header": [], "url": {"raw": "{{baseUrl}}/acl", "host": ["{{baseUrl}}"], "path": ["acl"]}}, "response": []}, {"name": "acl/:Id", "event": [{"listen": "test", "script": {"exec": [""], "type": "text/javascript", "packages": {}, "id": "07fff2ed-8324-4d9c-8c9a-8edf0fb27be3"}}], "id": "838766-461dce51-7924-4f15-a877-d0d62fb3f971", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/acl/:id", "host": ["{{baseUrl}}"], "path": ["acl", ":id"], "variable": [{"id": "a1362a43-42fe-4548-ab9e-493cd086ca3c", "key": "id", "value": ""}]}}, "response": []}, {"name": "acl/:Id", "event": [{"listen": "test", "script": {"exec": [""], "type": "text/javascript", "packages": {}, "id": "bd94b99c-4ace-4310-8dad-60ebaffba3be"}}], "id": "838766-67a13c6b-a62b-421a-b00c-6b29051ceb67", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "OPTIONS", "header": [], "url": {"raw": "{{baseUrl}}/acl/:id", "host": ["{{baseUrl}}"], "path": ["acl", ":id"], "variable": [{"id": "5fc1de69-6da0-4701-a318-83fe6c3686c5", "key": "id", "value": ""}]}}, "response": []}, {"name": "acl", "event": [{"listen": "test", "script": {"exec": [""], "type": "text/javascript", "packages": {}, "id": "d1254a8d-8576-4be4-88da-23047ab8bf60"}}], "id": "838766-fd5f152c-1407-4283-aa33-cb598b27f0cf", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\n    \"shortname\": \"testiang\",\n    \"description\": \"role x\",\n    \"name\": \"heyhey\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{baseUrl}}/acl", "host": ["{{baseUrl}}"], "path": ["acl"]}}, "response": []}, {"name": "acl/:id", "event": [{"listen": "test", "script": {"exec": [""], "type": "text/javascript", "packages": {}, "id": "453832d1-e862-47fb-8a85-4f2aedb28d90"}}], "id": "838766-d6c6cc83-bd73-42d2-a9e9-620ccdd1259d", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "PUT", "header": [], "body": {"mode": "raw", "raw": "{\n    \"shortname\": \"testiang\",\n    \"description\": \"role x\",\n    \"name\": \"heyhey\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{baseUrl}}/acl/:id", "host": ["{{baseUrl}}"], "path": ["acl", ":id"], "variable": [{"id": "a363e7e1-f7c6-4acd-bb00-a9cce11adbb6", "key": "id", "value": ""}]}}, "response": []}, {"name": "acl/:id", "event": [{"listen": "test", "script": {"exec": [""], "type": "text/javascript", "packages": {}, "id": "74cfa1a7-0032-4616-8f26-35a163a46189"}}], "id": "838766-334fcf58-d808-4e49-9163-bddf46e0432e", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "DELETE", "header": [], "url": {"raw": "{{baseUrl}}/acl/:id", "host": ["{{baseUrl}}"], "path": ["acl", ":id"], "variable": [{"id": "82a91679-dcdb-4373-ab84-c59aecc31b59", "key": "id", "value": ""}]}}, "response": []}], "id": "838766-9e8850ed-5715-44cd-ac5e-1b0a85e89b65"}, {"name": "Account", "item": [{"name": "accounts", "event": [{"listen": "test", "script": {"id": "9010b8b9-dcf8-4a42-8218-647c7e59f793", "exec": [""], "type": "text/javascript", "packages": {}}}], "id": "838766-65ebd05c-e797-40ee-ad74-8668d47c761c", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/accounts", "host": ["{{baseUrl}}"], "path": ["accounts"]}}, "response": []}, {"name": "accounts", "event": [{"listen": "test", "script": {"exec": [""], "type": "text/javascript", "packages": {}, "id": "4bf48f78-0773-4448-a2da-184f00820e6f"}}], "id": "838766-c280e9f0-c05d-4284-9b9b-d09dcc415339", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "OPTIONS", "header": [], "url": {"raw": "{{baseUrl}}/accounts", "host": ["{{baseUrl}}"], "path": ["accounts"]}}, "response": []}, {"name": "accounts/:Id", "event": [{"listen": "test", "script": {"exec": [""], "type": "text/javascript", "packages": {}, "id": "6e7f78b6-cdd4-495e-8097-aeae3be693f2"}}], "id": "838766-25be0e46-d226-446d-ab78-a0256bc60956", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/accounts/:id", "host": ["{{baseUrl}}"], "path": ["accounts", ":id"], "variable": [{"id": "39a88eab-8876-4f50-972e-ed2122224a2b", "key": "id", "value": ""}]}}, "response": []}, {"name": "accounts/:Id", "event": [{"listen": "test", "script": {"exec": [""], "type": "text/javascript", "packages": {}, "id": "4a384027-1ba6-4562-96ab-784486cc1256"}}], "id": "838766-73de1bc2-24bc-4e85-84e8-2a3de7f31bea", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "OPTIONS", "header": [], "url": {"raw": "{{baseUrl}}/accounts/:id", "host": ["{{baseUrl}}"], "path": ["accounts", ":id"], "variable": [{"id": "b02c79c1-7f3a-4f2e-a67a-f6469d942210", "key": "id", "value": ""}]}}, "response": []}, {"name": "accounts", "event": [{"listen": "test", "script": {"id": "db04c948-c814-486e-8a5a-08c5e578da5e", "exec": [""], "type": "text/javascript", "packages": {}}}], "id": "838766-20d23f62-f3f6-41fc-b947-4aede7ea3ac4", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\n    \"roleId\": \"12fe8ce3-3055-4531-8371-a78d2bbbbf95\",\n    \"email\": \"<EMAIL>\",\n    \"isActive\": true\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{baseUrl}}/accounts", "host": ["{{baseUrl}}"], "path": ["accounts"]}}, "response": []}, {"name": "accounts/:id", "event": [{"listen": "test", "script": {"id": "35e07648-929e-4fc3-8e05-a898bb8688d9", "exec": [""], "type": "text/javascript", "packages": {}}}], "id": "838766-8d1ddfcf-73ea-4272-aa55-afb7ee420095", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "PUT", "header": [], "body": {"mode": "raw", "raw": "{\n    \"firstName\": \"Test\",\n    \"email\": \"<EMAIL>\",\n    \"isActive\": true\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{baseUrl}}/accounts/:id", "host": ["{{baseUrl}}"], "path": ["accounts", ":id"], "variable": [{"key": "id", "value": "792dfd98-67e5-4482-91a6-376d9feeded6"}]}}, "response": []}, {"name": "accounts/:id", "event": [{"listen": "test", "script": {"exec": [""], "type": "text/javascript", "packages": {}, "id": "2efcadad-dc53-4fcd-9bf2-cf9ddd45c4f4"}}], "id": "838766-4c0bdf24-6acb-4007-84cb-15655eaaac12", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "DELETE", "header": [], "url": {"raw": "{{baseUrl}}/accounts/:id", "host": ["{{baseUrl}}"], "path": ["accounts", ":id"], "variable": [{"id": "f4413c87-c489-48ce-8e5d-eb6e3a2de8c4", "key": "id", "value": ""}]}}, "response": []}, {"name": "accounts/:id/activate", "event": [{"listen": "test", "script": {"exec": [""], "type": "text/javascript", "packages": {}, "id": "a7cd676d-6a41-407c-841c-1a17572d3c97"}}], "id": "838766-59efbbc5-7eab-43dd-bf70-f7269eb08f04", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "POST", "header": [], "url": {"raw": "{{baseUrl}}/accounts/:id/activate", "host": ["{{baseUrl}}"], "path": ["accounts", ":id", "activate"], "variable": [{"id": "b375cca8-5e6a-43a5-8078-25d79aa026a5", "key": "id", "value": ""}]}}, "response": []}, {"name": "accounts/:id/deactivate", "event": [{"listen": "test", "script": {"exec": [""], "type": "text/javascript", "packages": {}, "id": "17b899aa-83ce-49b4-8d70-8e146ed117e4"}}], "id": "838766-64f40944-dcd5-4e81-95fa-6dda04754513", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "POST", "header": [], "url": {"raw": "{{baseUrl}}/accounts/:id/deactivate", "host": ["{{baseUrl}}"], "path": ["accounts", ":id", "deactivate"], "variable": [{"id": "6eb2be73-ccbf-4dd5-bf33-22736ed9e895", "key": "id", "value": "9951972e-3413-48a8-80b7-731f10899d94"}]}}, "response": []}], "id": "838766-b3abc347-9b8b-464f-9318-238a263ae7c7"}], "auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{token}}", "type": "string"}]}, "event": [{"listen": "prerequest", "script": {"id": "bc1551dc-ad6b-4a75-8da7-a2a06d38967d", "type": "text/javascript", "packages": {}, "exec": [""]}}, {"listen": "test", "script": {"id": "39b16238-379f-425a-a990-a9316f5cbeec", "type": "text/javascript", "packages": {}, "exec": [""]}}], "variable": [{"key": "baseUrl", "value": "{{defaultBaseUrl}}/account", "type": "string"}]}