import type { PaginationParams } from '@thrift/common/engines/Pagination'
import {
  ResourceMessageCode,
  type ResourceMethodProps,
} from '@thrift/common/engines/Resource'
import { ResponseHelper } from '@thrift/common/engines/Resource/helpers/ResponseHelper'
import { Delete, Get, Post, Put } from '@thrift/common/engines/Server'

import {
  Create,
  type CreateAccountDto,
  Delete as DeleteAccount,
  Read,
  Update,
  type UpdateAccountDto,
} from '@app/application/Account'
import { Language } from '@app/application/Language'

import type { IdentifierDto } from '@app/resources/Account/Account'

export class AccountResource {
  @Get('/accounts/:id')
  public async getAccountById({ request, response }: ResourceMethodProps) {
    try {
      const { id } = request.params<IdentifierDto>()
      const accountData = await new Read().find(id)

      // Format data for frontend
      const formattedData = {
        id: accountData.id,
        status: accountData.isActive
          ? Language.translate('common:active')
          : Language.translate('common:inactive'),
        email: accountData.email,
        roleName: accountData.roleName,
        name: accountData.name || '',
      }

      ResponseHelper.sendSuccessResponse(
        response,
        formattedData,
        ResourceMessageCode.C_200_0200,
        Language.translate,
      )
    } catch (error) {
      ResponseHelper.handleError(error, response, 'account', Language.translate)
    }
  }

  @Get('/accounts')
  public async getAccounts({ request, response }: ResourceMethodProps) {
    try {
      const paginationParams = request.query<PaginationParams>()
      const accountsData = await new Read().findMultiple(paginationParams)

      // Format data for frontend
      const formattedData = {
        ...accountsData,
        items: accountsData.items.map((account) => ({
          id: account.id,
          status: account.isActive
            ? Language.translate('common:active')
            : Language.translate('common:inactive'),
          email: account.email,
          roleName: account.role?.name || '',
          name: '', // Note: Multiple accounts don't include person data by default
        })),
      }

      ResponseHelper.sendSuccessResponse(
        response,
        formattedData,
        ResourceMessageCode.C_200_0200,
        Language.translate,
        { title: 'account' },
      )
    } catch (error) {
      ResponseHelper.handleError(error, response, 'account', Language.translate)
    }
  }

  @Post('/accounts')
  public async postAccount({ request, response }: ResourceMethodProps) {
    try {
      const createAccountDto = request.body<CreateAccountDto>()

      response.send({
        code: ResourceMessageCode.C_201_0001,
        message: Language.translate(
          `resources:${ResourceMessageCode.C_201_0001}`,
          {
            title: 'account',
          },
        ),
        data: await new Create().create(createAccountDto),
      })
    } catch (error) {
      ResponseHelper.handleError(error, response, 'account', Language.translate)
    }
  }

  @Put('/accounts/:id')
  public async updateAccounts({ request, response }: ResourceMethodProps) {
    try {
      const { id } = request.params<IdentifierDto>()
      const updateAccountDto = request.body<UpdateAccountDto>()

      response.send({
        code: ResourceMessageCode.C_200_0001,
        message: Language.translate(
          `resources:${ResourceMessageCode.C_200_0001}`,
          {
            title: 'account',
          },
        ),
        data: await new Update().update({
          ...updateAccountDto,
          id,
        }),
      })
    } catch (error) {
      ResponseHelper.handleError(error, response, 'account', Language.translate)
    }
  }

  @Delete('/accounts/:id')
  public async deleteAccount({ request, response }: ResourceMethodProps) {
    try {
      const { id } = request.params<IdentifierDto>()

      await new DeleteAccount().delete(id)

      response.send({
        code: ResourceMessageCode.C_200_0001,
        message: Language.translate(
          `resources:${ResourceMessageCode.C_204_0001}`,
          {
            title: 'account',
          },
        ),
      })
    } catch (error) {
      ResponseHelper.handleError(error, response, 'account', Language.translate)
    }
  }
}
