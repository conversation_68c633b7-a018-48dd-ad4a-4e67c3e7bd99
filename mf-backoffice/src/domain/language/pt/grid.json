{"name": "Nome", "description": "Descrição", "variants": "Variações", "gridData": "<PERSON><PERSON> da <PERSON>", "characteristics": "Características", "fieldMax": "O campo deve ter no máximo {{max}} caracteres", "fieldMin": "O campo deve ter no mínimo {{min}} caracteres", "fieldRequired": "O campo é obrigatório", "emptyTable": "Nenhuma grade encontrada", "addSubmit": "Grade adicionada com sucesso", "editSubmit": "Grade editada com sucesso", "deleted": "Grade excluída com sucesso", "emptyTableCharacteristics": "Nenhuma característica encontrada", "addSubmitCharacteristics": "Característica adicionada com sucesso", "editSubmitCharacteristics": "Característica editada com sucesso", "deletedCharacteristics": "Característica excluída com sucesso", "errorSubmitting": "Erro ao enviar o formulário", "errorDeleting": "Erro ao excluir a grade", "errorDeletingCharacteristics": "Erro ao excluir a característica"}