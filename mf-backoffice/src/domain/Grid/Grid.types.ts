interface Grid {
  id: string
  name: string
  description?: string
}

interface GridListParams {
  id: string
  page: number
  perPage: number
}

interface GridCharacteristics {
  id: string
  name: string
  variants: string
}

interface GridCharacteristicsListParams {
  id: string
  page: number
  perPage: number
}

export type {
  Grid,
  GridListParams,
  GridCharacteristics,
  GridCharacteristicsListParams,
}
