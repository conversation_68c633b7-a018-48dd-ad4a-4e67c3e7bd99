import { api, type ApiResponse } from '@app/domain/services'
import type {
  Grid,
  GridCharacteristics,
  GridCharacteristicsListParams,
  GridListParams,
} from '@app/domain/Grid'

export const getGrids = async (params: GridListParams) => {
  const response = await api.get<ApiResponse<Grid[]>>(
    `/user?page=${params.page}&perPage=${params.perPage}`,
  )

  return response?.data?.data
}

export const getGridById = async (id: string) => {
  const response = await api.get<ApiResponse<Grid>>(`/user/${id}`)

  return response?.data?.data
}

export const postGrid = async (params: Omit<Grid, 'id'>) => {
  const response = await api.post<ApiResponse<Grid>>('/user', params)

  return response?.data?.data
}

export const putGrid = async (params: Grid) => {
  const { id, ...body } = params

  const response = await api.put<ApiResponse<Grid>>(`/user/${id}`, body)

  return response?.data?.data
}

export const deleteGrid = async (id: string) => {
  const response = await api.delete<ApiResponse<void>>(`/user/${id}`)

  return response?.data?.data
}

export const getGridsCharacteristics = async (
  params: GridCharacteristicsListParams,
) => {
  const response = await api.get<ApiResponse<GridCharacteristics[]>>(
    `/user?page=${params.page}&perPage=${params.perPage}`,
  )

  return response?.data?.data
}

export const getGridCharacteristicsById = async (id: string) => {
  const response = await api.get<ApiResponse<GridCharacteristics>>(
    `/user/${id}`,
  )

  return response?.data?.data
}

export const postGridCharacteristics = async (
  params: Omit<GridCharacteristics, 'id'>,
) => {
  const response = await api.post<ApiResponse<GridCharacteristics>>(
    '/user',
    params,
  )

  return response?.data?.data
}

export const putGridCharacteristics = async (params: GridCharacteristics) => {
  const { id, ...body } = params

  const response = await api.put<ApiResponse<GridCharacteristics>>(
    `/user/${id}`,
    body,
  )

  return response?.data?.data
}

export const deleteGridCharacteristics = async (id: string) => {
  const response = await api.delete<ApiResponse<void>>(`/user/${id}`)

  return response?.data?.data
}
