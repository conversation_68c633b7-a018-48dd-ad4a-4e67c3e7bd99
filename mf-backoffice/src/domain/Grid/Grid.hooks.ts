import { useCallback } from 'react'
import { useMutation, useQuery } from '@tanstack/react-query'
import {
  postGrid,
  putGrid,
  getGrids,
  getGridById,
  deleteGrid,
  getGridsCharacteristics,
  getGridCharacteristicsById,
  postGridCharacteristics,
  putGridCharacteristics,
  deleteGridCharacteristics,
  type GridListParams,
  type Grid,
  type GridCharacteristics,
  type GridCharacteristicsListParams,
} from '@app/domain/Grid'

export const useGetGridsQuery = (params: GridListParams) => {
  const getGridsFn = useQuery<Grid[]>({
    queryKey: ['getGrids', params],
    queryFn: () => getGrids(params),
    enabled: params !== null,
  })

  return getGridsFn
}

export const useGetGridByIdQuery = (id: string) => {
  const getGridByIdFn = useQuery<Grid>({
    queryKey: ['getGridById', id],
    queryFn: () => getGridById(id),
    enabled: !!id,
  })

  return getGridByIdFn
}

export const usePostGridMutation = () => {
  const { mutateAsync, isPending } = useMutation({
    mutationFn: (params: Omit<Grid, 'id'>) => postGrid(params),
  })

  const postGridAsync = useCallback(
    async (props: Omit<Grid, 'id'>) => {
      const response = await mutateAsync(props)

      return response
    },
    [mutateAsync],
  )

  return {
    postGridAsync,
    loading: isPending,
  }
}

export const usePutGridMutation = () => {
  const { mutateAsync, isPending } = useMutation({
    mutationFn: (params: Grid) => putGrid(params),
  })

  const putGridAsync = useCallback(
    async (props: Grid) => {
      const response = await mutateAsync(props)

      return response
    },
    [mutateAsync],
  )

  return {
    putGridAsync,
    loading: isPending,
  }
}

export const useDeleteGridMutation = () => {
  const { mutateAsync, isPending } = useMutation({
    mutationFn: (id: string) => deleteGrid(id),
  })

  const deleteGridAsync = useCallback(
    async (props: string) => {
      const response = await mutateAsync(props)

      return response
    },
    [mutateAsync],
  )

  return {
    deleteGridAsync,
    loading: isPending,
  }
}

export const useGetGridsCharacteristicsQuery = (
  params: GridCharacteristicsListParams,
) => {
  const getGridsCharacteristicsFn = useQuery<GridCharacteristics[]>({
    queryKey: ['getGridsCharacteristics', params],
    queryFn: () => getGridsCharacteristics(params),
    enabled: params !== null,
  })

  return getGridsCharacteristicsFn
}

export const useGetGridCharacteristicsByIdQuery = (id: string) => {
  const getGridCharacteristicsByIdFn = useQuery<GridCharacteristics>({
    queryKey: ['getGridCharacteristicsById', id],
    queryFn: () => getGridCharacteristicsById(id),
    enabled: !!id,
  })

  return getGridCharacteristicsByIdFn
}

export const usePostGridCharacteristicsMutation = () => {
  const { mutateAsync, isPending } = useMutation({
    mutationFn: (params: Omit<GridCharacteristics, 'id'>) =>
      postGridCharacteristics(params),
  })

  const postGridCharacteristicsAsync = useCallback(
    async (props: Omit<GridCharacteristics, 'id'>) => {
      const response = await mutateAsync(props)

      return response
    },
    [mutateAsync],
  )

  return {
    postGridCharacteristicsAsync,
    loading: isPending,
  }
}

export const usePutGridCharacteristicsMutation = () => {
  const { mutateAsync, isPending } = useMutation({
    mutationFn: (params: GridCharacteristics) => putGridCharacteristics(params),
  })

  const putGridCharacteristicsAsync = useCallback(
    async (props: GridCharacteristics) => {
      const response = await mutateAsync(props)

      return response
    },
    [mutateAsync],
  )

  return {
    putGridCharacteristicsAsync,
    loading: isPending,
  }
}

export const useDeleteGridCharacteristicsMutation = () => {
  const { mutateAsync, isPending } = useMutation({
    mutationFn: (id: string) => deleteGridCharacteristics(id),
  })

  const deleteGridCharacteristicsAsync = useCallback(
    async (props: string) => {
      const response = await mutateAsync(props)

      return response
    },
    [mutateAsync],
  )

  return {
    deleteGridCharacteristicsAsync,
    loading: isPending,
  }
}
