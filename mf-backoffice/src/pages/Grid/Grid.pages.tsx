import { Table } from '@thrift/design-system/packages/molecules/Table'
import { MoreActions } from '@thrift/design-system/packages/molecules/MoreActions'
import { useLanguage } from '@app/application/Language'
import { useNavigate } from 'react-router-dom'
import { useGrid } from '@app/application/Grid'

const Grid = () => {
  const { loadMore, isLoading, onDelete } = useGrid()
  const { translate } = useLanguage()
  const navigate = useNavigate()

  return (
    <Table
      columns={[
        { header: translate('grid:name'), accessor: 'name' },
        {
          header: translate('grid:characteristics'),
          accessor: 'characteristics',
        },
      ]}
      data={[]}
      onAdd={() => navigate('/grids/form')}
      onFilter={() => alert('Filtro clicado')}
      renderActions={(row) => (
        <MoreActions
          options={[
            {
              label: translate('titles.edit'),
              onClick: () => navigate(`/grids/form/${row.id}`),
            },
            {
              label: translate('titles.delete'),
              onClick: () => onDelete(row.id as string),
            },
          ]}
        />
      )}
      onEndReached={loadMore}
      isLoading={isLoading}
      labelEmpty={translate('grid:emptyTable')}
    />
  )
}

export { Grid }
