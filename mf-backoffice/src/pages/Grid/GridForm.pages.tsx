import { useLanguage } from '@app/application/Language'
import { Tabs } from '@thrift/design-system/packages/molecules/Tabs'
import { useNavigate } from 'react-router-dom'
import { useGrid } from '@app/application/Grid'

import { InputText } from '@thrift/design-system/packages/molecules/InputText'
import { Button } from '@thrift/design-system/packages/molecules/Button'
import { MoreActions } from '@thrift/design-system/packages/molecules/MoreActions'
import { Table } from '@thrift/design-system/packages/molecules/Table'
import { Drawer } from '@thrift/design-system/packages/molecules/Drawer'

const GridForm = () => {
  const {
    register,
    registerCharacteristics,
    handleSubmit,
    handleSubmitCharacteristics,
    errors,
    errorsCharacteristics,
    index,
    setIndex,
    isOpenDrawer,
    setIsOpenDrawer,
    loadMoreCharacteristics,
    isLoading,
    onDeleteCharacteristic,
    resetCharacteristics,
  } = useGrid()

  const navigate = useNavigate()
  const { translate } = useLanguage()

  return (
    <div className="flex flex-col h-full">
      <Drawer
        isOpen={isOpenDrawer}
        title={translate('grid:characteristics')}
        onClose={() => {
          setIsOpenDrawer(false)
          resetCharacteristics()
        }}
      >
        <div className="flex flex-col gap-2">
          <InputText
            {...registerCharacteristics('nameCharacteristics')}
            label={translate('grid:name')}
            errorMessage={errorsCharacteristics.nameCharacteristics?.message}
          />
          <InputText
            {...registerCharacteristics('variants')}
            label={translate('grid:variants')}
            errorMessage={errorsCharacteristics.variants?.message}
          />
          <div className="w-full flex justify-end mt-[10rem]">
            <div className="w-full max-w-[300px] flex flex-col md:flex-row gap-2">
              <Button
                variant="secondary"
                onClick={() => {
                  setIsOpenDrawer(false)
                  resetCharacteristics()
                }}
              >
                Cancelar
              </Button>
              <Button variant="primary" onClick={handleSubmitCharacteristics}>
                Salvar
              </Button>
            </div>
          </div>
        </div>
      </Drawer>
      <Tabs
        tabs={[
          {
            label: translate('grid:gridData'),
            content: (
              <div className="flex flex-col h-full">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <InputText
                    {...register('name')}
                    label={translate('grid:name')}
                    errorMessage={errors.name?.message}
                  />
                  <InputText
                    {...register('description')}
                    label={translate('grid:description')}
                    errorMessage={errors.description?.message}
                  />
                </div>
                <div className="flex-grow" />
                <div className="w-full flex justify-end">
                  <div className="w-full max-w-[300px] flex flex-col md:flex-row gap-2">
                    <Button variant="secondary" onClick={() => navigate(-1)}>
                      Cancelar
                    </Button>
                    <Button variant="primary" onClick={handleSubmit}>
                      Salvar
                    </Button>
                  </div>
                </div>
              </div>
            ),
          },
          {
            label: translate('grid:characteristics'),
            content: (
              <Table
                columns={[
                  { header: translate('grid:name'), accessor: 'name' },
                  { header: translate('grid:variants'), accessor: 'variants' },
                ]}
                data={[]}
                onAdd={() => setIsOpenDrawer(true)}
                onFilter={() => alert('Filtro clicado')}
                renderActions={(row) => (
                  <MoreActions
                    options={[
                      {
                        label: translate('titles.edit'),
                        onClick: () => setIsOpenDrawer(true),
                      },
                      {
                        label: translate('titles.delete'),
                        onClick: () => onDeleteCharacteristic(row.id as string),
                      },
                    ]}
                  />
                )}
                onEndReached={loadMoreCharacteristics}
                isLoading={isLoading}
                labelEmpty={translate('grid:emptyTableCharacteristics')}
              />
            ),
          },
        ]}
        activeIndex={index}
        onChange={setIndex}
      />
    </div>
  )
}

export { GridForm }
