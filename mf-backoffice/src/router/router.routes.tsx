import { createBrowserRouter, Navigate } from 'react-router-dom'

import { PrivateRoute } from '@app/pages/PrivateRoute'
import { LoggedLayout } from '@app/pages/LoggedLayout'

import { Dashboard } from '@app/pages/Dashboard'
import { Stock, StockForm } from '@app/pages/Stock'
import { Product, ProductForm } from '@app/pages/Product'
import { Grid, GridForm } from '@app/pages/Grid'
import { Category, CategoryForm } from '@app/pages/Category'
import { User, UserForm } from '@app/pages/User'
import { Units } from '@app/pages/Units'
import { Tag, TagForm } from '@app/pages/Tag'

export const router = createBrowserRouter(
  [
    {
      element: <PrivateRoute />,
      children: [
        {
          element: <LoggedLayout />,
          children: [
            {
              path: '/',
              element: <Dashboard />,
              handle: { pageTitle: 'titles:dashboard' },
            },
            {
              path: '/stock',
              element: <Stock />,
              handle: { pageTitle: 'titles:stock', menuPath: '/stock' },
            },
            {
              path: '/stock/form/:id?',
              element: <StockForm />,
              handle: {
                pageTitle: {
                  create: 'titles:add',
                  edit: 'titles:edit',
                },
                menuPath: '/stock',
              },
            },
            {
              path: '/product',
              element: <Product />,
              handle: { pageTitle: 'titles:product' },
            },
            {
              path: '/product/form/:id?',
              element: <ProductForm />,
              handle: {
                pageTitle: {
                  create: 'titles:add',
                  edit: 'titles:edit',
                },
                menuPath: '/product',
              },
            },
            {
              path: '/grids',
              element: <Grid />,
              handle: { pageTitle: 'titles:grid' },
            },
            {
              path: '/grids/form/:id?',
              element: <GridForm />,
              handle: {
                pageTitle: {
                  create: 'titles:add',
                  edit: 'titles:edit',
                },
                menuPath: '/grids',
              },
            },
            {
              path: '/categories',
              element: <Category />,
              handle: { pageTitle: 'titles:categories' },
            },
            {
              path: '/categories/form/:id?',
              element: <CategoryForm />,
              handle: {
                pageTitle: {
                  create: 'titles:add',
                  edit: 'titles:edit',
                },
                menuPath: '/categories',
              },
            },
            {
              path: '/tags',
              element: <Tag />,
              handle: { pageTitle: 'titles:tags' },
            },
            {
              path: '/tags/form/:id?',
              element: <TagForm />,
              handle: {
                pageTitle: {
                  create: 'titles:add',
                  edit: 'titles:edit',
                },
                menuPath: '/tags',
              },
            },
            {
              path: '/users',
              element: <User />,
              handle: { pageTitle: 'titles:users' },
            },
            {
              path: '/users/form/:id?',
              element: <UserForm />,
              handle: {
                pageTitle: {
                  create: 'titles:add',
                  edit: 'titles:edit',
                },
                menuPath: '/users',
              },
            },
            {
              path: '/units',
              element: <Units />,
              handle: { pageTitle: 'titles:units' },
            },
          ],
        },
      ],
    },
    {
      path: '*',
      element: <Navigate to="/" replace />,
    },
  ],
  {
    basename: '/backoffice',
  },
)
