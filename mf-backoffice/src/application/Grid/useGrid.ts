/* eslint-disable max-lines */
import { useToast } from '@thrift/design-system/packages/molecules/Toast'
import { useLanguage } from '@app/application/Language'
import { useEffect, useState } from 'react'
import {
  type UseGridSchemaType,
  type UseGridCharacteristicsSchemaType,
  useGridCharacteristicsSchema,
  useGridSchema,
} from '@app/application/Grid'
import { zodResolver } from '@hookform/resolvers/zod'
import { useForm } from 'react-hook-form'
import { useParams } from 'react-router-dom'
import {
  useDeleteGridMutation,
  useGetGridByIdQuery,
  useGetGridsQuery,
  usePostGridMutation,
  usePutGridMutation,
  useDeleteGridCharacteristicsMutation,
  useGetGridCharacteristicsByIdQuery,
  useGetGridsCharacteristicsQuery,
  usePostGridCharacteristicsMutation,
  usePutGridCharacteristicsMutation,
  type Grid,
  type GridCharacteristics,
} from '@app/domain/Grid'
import { useAuthStore } from '@app/domain/stores/Auth'

export const useGrid = () => {
  const { id } = useParams()
  const { showToast } = useToast()
  const { translate } = useLanguage()
  const GridSchema = useGridSchema()
  const GridCharacteristicsSchema = useGridCharacteristicsSchema()
  const [page, setPage] = useState(1)
  const [pageCharacteristics, setPageCharacteristics] = useState(1)
  const [items, setItems] = useState<Grid[]>([])

  const [characteristicItems, setCharacteristicItems] = useState<
    GridCharacteristics[]
  >([])

  const tenant = useAuthStore((state) => state.tenant)
  const { postGridAsync } = usePostGridMutation()
  const { putGridAsync } = usePutGridMutation()
  const { deleteGridAsync } = useDeleteGridMutation()
  const { postGridCharacteristicsAsync } = usePostGridCharacteristicsMutation()
  const { putGridCharacteristicsAsync } = usePutGridCharacteristicsMutation()

  const { deleteGridCharacteristicsAsync } =
    useDeleteGridCharacteristicsMutation()

  const [index, setIndex] = useState(0)
  const [gridId, setGridId] = useState<string>(id ?? '')

  const [gridCharacteristicId, setGridCharacteristicId] = useState<string>('')

  const [isOpenDrawer, setIsOpenDrawer] = useState<boolean>(false)

  const { data: gridData, isLoading: isLoadingGrid } =
    useGetGridByIdQuery(gridId)

  const {
    data: gridDataCharacteristics,
    isLoading: isLoadingGridCharacteristicsById,
  } = useGetGridCharacteristicsByIdQuery(gridCharacteristicId)

  const { data, isLoading } = useGetGridsQuery({
    id: tenant,
    page,
    perPage: 10,
  })

  const {
    data: gridCharacteristicsData,
    isLoading: isLoadingGridCharacteristics,
  } = useGetGridsCharacteristicsQuery({
    id: tenant,
    page: pageCharacteristics,
    perPage: 10,
  })

  const {
    register,
    handleSubmit,
    formState: { errors },
    reset,
  } = useForm<UseGridSchemaType>({
    resolver: zodResolver(GridSchema),
    defaultValues: {
      name: '',
      description: '',
    },
  })

  const {
    register: registerCharacteristics,
    handleSubmit: handleSubmitCharacteristics,
    formState: { errors: errorsCharacteristics },
    reset: resetCharacteristics,
  } = useForm<UseGridCharacteristicsSchemaType>({
    resolver: zodResolver(GridCharacteristicsSchema),
    defaultValues: {
      nameCharacteristics: '',
      variants: '',
    },
  })

  useEffect(() => {
    if (data && Array.isArray(data)) {
      setItems((prev) => [...prev, ...data])
    }
  }, [data])

  useEffect(() => {
    if (gridCharacteristicsData && Array.isArray(gridCharacteristicsData)) {
      setCharacteristicItems((prev) => [...prev, ...gridCharacteristicsData])
    }
  }, [gridCharacteristicsData])

  useEffect(() => {
    if (gridData) {
      reset({
        name: gridData.name ?? '',
        description: gridData.description ?? '',
      })
    }
  }, [gridData, reset])

  useEffect(() => {
    if (gridDataCharacteristics) {
      resetCharacteristics({
        nameCharacteristics: gridDataCharacteristics.name ?? '',
        variants: gridDataCharacteristics.variants ?? '',
      })
    }
  }, [gridDataCharacteristics, resetCharacteristics])

  const onSubmitForm = async (formData: UseGridSchemaType) => {
    try {
      if (gridId) {
        await putGridAsync({
          ...formData,
          id: gridId,
        })
      } else {
        const response = await postGridAsync(formData)

        setGridId(response.id)
      }

      showToast({
        type: 'success',
        message: translate('grid:formSubmitted'),
      })
    } catch {
      showToast({
        type: 'error',
        message: translate('grid:errorSubmitting'),
      })
    }
  }

  const onSubmitFormCharacteristics = async (
    formData: UseGridCharacteristicsSchemaType,
  ) => {
    try {
      if (gridCharacteristicId) {
        await putGridCharacteristicsAsync({
          variants: formData.variants,
          name: formData.nameCharacteristics,
          id: gridCharacteristicId,
        })
      } else {
        const response = await postGridCharacteristicsAsync({
          variants: formData.variants,
          name: formData.nameCharacteristics,
        })

        setGridCharacteristicId(response.id)
      }
      resetCharacteristics()
      showToast({
        type: 'success',
        message: translate('grid:formSubmitted'),
      })
    } catch {
      showToast({
        type: 'error',
        message: translate('grid:errorSubmitting'),
      })
    }
  }

  const loadMore = () => {
    const nextPage = page + 1

    setPage(nextPage)
  }

  const loadMoreCharacteristics = () => {
    const nextPage = pageCharacteristics + 1

    setPageCharacteristics(nextPage)
  }

  const onDelete = async (id: string) => {
    try {
      await deleteGridAsync(id)
      setItems((prev) => prev.filter((item) => item.id !== id))
      showToast({
        type: 'success',
        message: translate('grid:deleted'),
      })
    } catch {
      showToast({
        type: 'error',
        message: translate('grid:errorDeleting'),
      })
    }
  }

  const onDeleteCharacteristic = async (id: string) => {
    try {
      await deleteGridCharacteristicsAsync(id)
      setCharacteristicItems((prev) => prev.filter((item) => item.id !== id))
      showToast({
        type: 'success',
        message: translate('grid:deletedCharacteristics'),
      })
    } catch {
      showToast({
        type: 'error',
        message: translate('grid:errorDeletingCharacteristics'),
      })
    }
  }

  return {
    loadMore,
    loadMoreCharacteristics,
    isLoading:
      isLoading ||
      isLoadingGrid ||
      isLoadingGridCharacteristics ||
      isLoadingGridCharacteristicsById,
    register,
    registerCharacteristics,
    handleSubmit: handleSubmit(onSubmitForm),
    errors,
    items,
    characteristicItems,
    onDelete,
    onDeleteCharacteristic,
    index,
    setIndex,
    gridId,
    isOpenDrawer,
    setIsOpenDrawer,
    handleSubmitCharacteristics: handleSubmitCharacteristics(
      onSubmitFormCharacteristics,
    ),
    errorsCharacteristics,
    resetCharacteristics,
    setGridCharacteristicId,
    gridCharacteristicId,
  }
}
