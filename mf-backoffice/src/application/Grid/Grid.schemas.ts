import { z } from 'zod'
import { useLanguage } from '@app/application/Language'
import { InputTextSchema } from '@thrift/design-system/packages/molecules/InputText'

export const useGridSchema = () => {
  const { translate } = useLanguage()

  return z.object({
    name: InputTextSchema({
      max: { value: 50, message: translate('grid:fieldMax', { max: 50 }) },
      min: { value: 0, message: translate('grid:fieldMin', { min: 0 }) },
      required: { value: true, message: translate('grid:fieldRequired') },
    }),
    description: InputTextSchema({
      max: { value: 50, message: translate('grid:fieldMax', { max: 50 }) },
      min: { value: 0, message: translate('grid:fieldMin', { min: 0 }) },
      required: { value: true, message: translate('grid:fieldRequired') },
    }),
  })
}

export const useGridCharacteristicsSchema = () => {
  const { translate } = useLanguage()

  return z.object({
    nameCharacteristics: InputTextSchema({
      max: { value: 50, message: translate('grid:fieldMax', { max: 50 }) },
      min: { value: 0, message: translate('grid:fieldMin', { min: 0 }) },
      required: { value: true, message: translate('grid:fieldRequired') },
    }),
    variants: InputTextSchema({
      max: { value: 50, message: translate('grid:fieldMax', { max: 50 }) },
      min: { value: 0, message: translate('grid:fieldMin', { min: 0 }) },
      required: { value: true, message: translate('grid:fieldRequired') },
    }),
  })
}

export type UseGridSchemaType = z.infer<ReturnType<typeof useGridSchema>>
export type UseGridCharacteristicsSchemaType = z.infer<
  ReturnType<typeof useGridCharacteristicsSchema>
>
