import { Login } from '@app/pages/Login'
import { Navigate, Route, Routes } from 'react-router-dom'
import { Tenant } from '@app/pages/Tenant'
import { Home } from '@app/pages/Home'

const App = () => {
  return (
    <Routes>
      <Route path="/" element={<Home />} />
      <Route path="/company" element={<Tenant />} />
      <Route path="/login" element={<Login />} />

      <Route path="*" element={<Navigate to="/" />} />
    </Routes>
  )
}

export default App
