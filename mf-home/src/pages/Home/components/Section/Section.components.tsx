import { forwardRef, type ReactNode } from 'react'
import { Typography } from '@thrift/design-system/packages/molecules/Typography'

type SectionProps = {
  id: string
  title?: string
  subtitle?: string
  bgColor?: string
  textColor?: string
  children: ReactNode
}

export const Section = forwardRef<HTMLElement, SectionProps>(
  ({ id, title, subtitle, bgColor, textColor, children }, ref) => {
    return (
      <div
        id={id}
        ref={ref as React.Ref<HTMLDivElement>}
        className={`flex flex-col justify-center items-center text-center py-16 ${(bgColor && bgColor) || 'bg-gray-100'} ${(textColor && textColor) || 'text-gray-950'}`}
      >
        {title && (
          <Typography
            variant="h2"
            weight="bold"
            align="center"
            className="mb-10"
          >
            {title}
          </Typography>
        )}

        {subtitle && (
          <Typography
            variant="h5"
            weight="regular"
            align="center"
            className="mb-10"
          >
            {subtitle}
          </Typography>
        )}

        {children}
      </div>
    )
  },
)
