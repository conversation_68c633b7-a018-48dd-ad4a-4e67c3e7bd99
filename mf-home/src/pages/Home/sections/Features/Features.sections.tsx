import { forwardRef } from 'react'
import { CircleCheckBig } from 'lucide-react'
import { Typography } from '@thrift/design-system/packages/molecules/Typography'
import { Section } from '@app/pages/Home/components/Section'

const features = [
  { title: 'Gestão de Estoque Inteligente' },
  { title: 'Precificação Automática' },
  { title: 'Controle de Compras e Vendas' },
]

export const Features = forwardRef<HTMLElement>((props, ref) => {
  return (
    <Section id="features" bgColor="bg-white" ref={ref} {...props}>
      <div className="max-w-4xl mx-auto grid grid-cols-1 md:grid-cols-3 gap-8">
        {features.map((feature, index) => (
          <div
            key={index}
            className="flex flex-col justify-center items-center"
          >
            <CircleCheckBig color="#00C951" className="mb-2" />
            <Typography variant="p" weight="semibold" align="center">
              {feature.title}
            </Typography>
          </div>
        ))}
      </div>
    </Section>
  )
})
