import { forwardRef } from 'react'
import { SocialIcon } from 'react-social-icons'
import { useLanguage } from '@app/application/Language'
import { Typography } from '@thrift/design-system/packages/molecules/Typography'
import { Section } from '@app/pages/Home/components/Section'

export const Footer = forwardRef<HTMLElement>((props, ref) => {
  const { translate } = useLanguage()

  return (
    <Section
      id="footer"
      bgColor="bg-secondary"
      textColor="text-white"
      ref={ref}
      {...props}
    >
      <div className="max-w-6xl mx-auto py-8 px-6 flex flex-col md:flex-row justify-between items-center gap-6">
        <Typography variant="p" weight="regular" className="md:text-left">
          {translate('home:footerText')}
        </Typography>

        <div className="flex gap-4">
          <SocialIcon
            url="https://www.linkedin.com/company/thrift-technology"
            style={{ height: 30, width: 30 }}
          />
          <SocialIcon
            url="https://instagram.com/thrift.technology"
            style={{ height: 30, width: 30 }}
          />
          <SocialIcon
            url="https://www.threads.com/@thrift.technology"
            style={{ height: 30, width: 30 }}
          />
        </div>
      </div>
    </Section>
  )
})
