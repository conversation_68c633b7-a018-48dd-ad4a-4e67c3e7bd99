import { useState } from 'react'
import { useNavigate } from 'react-router-dom'
import { Menu, X } from 'lucide-react'
import { useLanguage } from '@app/application/Language'
import {
  Logo,
  LogoModeOptions,
  LogoVariantOptions,
} from '@thrift/design-system/packages/molecules/Logo'
import { Button } from '@thrift/design-system/packages/molecules/Button'
import { Typography } from '@thrift/design-system/packages/molecules/Typography'

type HeaderProps = {
  onHomeClick?: () => void
  onFeaturesClick?: () => void
  onPlansClick?: () => void
  onContactClick: () => void
}

export const Header: React.FC<HeaderProps> = ({
  onContactClick,
  onPlansClick,
  onFeaturesClick,
  onHomeClick,
}) => {
  const [open, setOpen] = useState(false)
  const navigate = useNavigate()
  const { translate } = useLanguage()

  const handleClick = () => {
    navigate('/company')
  }

  const links = [
    {
      name: translate('home:headerHome'),
      onClick: onHomeClick,
    },
    {
      name: translate('home:headerAbout'),
      onClick: onFeaturesClick,
    },
    {
      name: translate('home:headerPlans'),
      onClick: onPlansClick,
    },
    {
      name: translate('home:headerContact'),
      onClick: onContactClick,
    },
  ]

  return (
    <header className="bg-primary text-white fixed w-full top-0 z-50 px-12 py-12">
      <div className="mx-auto flex justify-around items-center">
        <div className="flex items-center gap-2">
          <Logo
            variant={LogoVariantOptions.LEFT_RIGHT}
            mode={LogoModeOptions.DARK}
            size="w-[15rem]"
          />
        </div>

        <nav className="hidden md:flex gap-10">
          {links.map((link, key) => (
            <button
              key={key}
              onClick={link.onClick}
              className="hover:text-gray-600 transition cursor-pointer"
            >
              <Typography weight="semibold">{link.name}</Typography>
            </button>
          ))}
        </nav>

        <div className="w-full max-w-[100px] flex flex-col md:flex-row gap-2">
          <Button variant="positive" onClick={handleClick}>
            Login
          </Button>
        </div>

        <button
          onClick={() => setOpen(!open)}
          className="md:hidden focus:outline-none"
        >
          {open ? <X size={28} /> : <Menu size={28} />}
        </button>
      </div>

      {open && (
        <div className="md:hidden bg-primary text-white flex flex-col items-center gap-4 pt-8">
          {links.map((link, key) => (
            <button
              key={key}
              onClick={link.onClick}
              className="hover:text-gray-600 transition cursor-pointer"
            >
              <Typography weight="semibold">{link.name}</Typography>
            </button>
          ))}
        </div>
      )}
    </header>
  )
}
