import { forwardRef } from 'react'
import { CircleCheck } from 'lucide-react'
import { useLanguage } from '@app/application/Language'
import { Typography } from '@thrift/design-system/packages/molecules/Typography'
import { Button } from '@thrift/design-system/packages/molecules/Button'
import { Section } from '@app/pages/Home/components/Section'

const plans = [
  {
    name: 'Plano Iniciante',
    price: 'R$99',
    features: [
      'Gestão de estoque',
      'Cadastro de clientes',
      'Relatórios básicos',
    ],
    active: false,
  },
  {
    name: 'Plano Pro',
    price: 'R$199',
    features: [
      'Controle automático',
      'Precificação automática',
      'Integração com e-commerce',
    ],
    active: false,
  },
  {
    name: 'Plano Premium',
    price: 'R$299',
    features: ['Tudo do Pro', 'Inteligência de vendas', 'Suporte prioritário'],
    active: true,
  },
]

export const Pricing = forwardRef<HTMLElement>((props, ref) => {
  const { translate } = useLanguage()

  return (
    <Section
      id="plans"
      title={`${translate('home:pricingTitle')}`}
      ref={ref}
      {...props}
    >
      <div className="max-w-8xl mx-auto grid md:grid-cols-3 gap-6">
        {plans.map((plan, i) => (
          <div
            key={i}
            className={`bg-white shadow-md rounded-lg p-6 ${plan.active && 'border-1'}`}
            style={{ borderColor: `${plan.active && '#0BB780'}` }}
          >
            <Typography variant="h5" weight="semibold" className="mb-6">
              {plan.name}
            </Typography>
            <div className="flex flex-row justify-right items-center">
              <Typography variant="h2" weight="bold" className="mb-6">
                {plan.price}
              </Typography>
              <Typography
                variant="p"
                weight="regular"
                className="text-gray-700"
              >
                / mês
              </Typography>
            </div>
            <Typography variant="p" weight="bold" className="mb-6">
              {translate('home:pricingText')}
            </Typography>

            {plan.features.map((f, idx) => (
              <div
                key={idx}
                className="flex flex-row justify-right items-center"
              >
                <CircleCheck size={16} color="#025E53" className="mr-4" />
                <Typography
                  variant="p"
                  weight="regular"
                  className="text-gray-900"
                >
                  {f}
                </Typography>
              </div>
            ))}

            <div className="w-full flex justify-center mt-15">
              <div className="w-full max-w-[200px] flex flex-col md:flex-row gap-4">
                <Button variant="positive">
                  {translate('home:pricingButton')}
                </Button>
              </div>
            </div>
          </div>
        ))}
      </div>
    </Section>
  )
})
