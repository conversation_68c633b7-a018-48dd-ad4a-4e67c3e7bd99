import { Typography } from '@thrift/design-system/packages/molecules/Typography'
import { Button } from '@thrift/design-system/packages/molecules/Button'
import background from '@app/pages/Home/sections/Banner/banner.jpg'
import { useLanguage } from '@app/application/Language'

type HeroProps = {
  onContactClick: () => void
}

export const Banner: React.FC<HeroProps> = ({ onContactClick }) => {
  const { translate } = useLanguage()

  return (
    <section
      id="home"
      className="relative w-full h-[50vh] flex items-center justify-center"
    >
      <img
        src={`${background}`}
        alt="Brechó inteligente"
        className="absolute inset-0 w-full h-full object-cover brightness-50"
      />

      <div className="absolute inset-0 flex flex-col justify-center items-center text-center">
        <Typography
          variant="h1"
          weight="bold"
          className="text-white mb-10 mt-30"
          align="center"
        >
          {translate('home:bannerTitle')}
        </Typography>
        <Typography
          variant="p"
          weight="semibold"
          className="text-white mb-10"
          align="center"
        >
          {translate('home:bannerSubtitle')}
        </Typography>

        <div className="w-full flex justify-center">
          <div className="w-full max-w-[200px] flex flex-col md:flex-row gap-4">
            <Button variant="secondary" onClick={onContactClick}>
              {translate('home:bannerButton')}
            </Button>
          </div>
        </div>
      </div>
    </section>
  )
}
