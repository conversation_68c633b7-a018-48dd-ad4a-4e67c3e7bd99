import { forwardRef } from 'react'
import { motion } from 'framer-motion'
import { Section } from '@app/pages/Home/components/Section'
import logo1 from '@app/pages/Home/sections/ClientsCarousel/clients/drywash-logo.webp'
import logo2 from '@app/pages/Home/sections/ClientsCarousel/clients/itaipu-logo.webp'
import logo3 from '@app/pages/Home/sections/ClientsCarousel/clients/locaweb-logo.webp'
import logo4 from '@app/pages/Home/sections/ClientsCarousel/clients/ifood-logo.webp'
import logo5 from '@app/pages/Home/sections/ClientsCarousel/clients/takeat-logo.webp'

const logos = [logo1, logo2, logo3, logo4, logo5]

export const ClientsCarousel = forwardRef<HTMLElement>((props, ref) => {
  return (
    <Section id="clients" bgColor="bg-white" ref={ref} {...props}>
      <div className="overflow-hidden relative max-w-6xl mx-auto">
        <motion.div
          className="flex space-x-12"
          animate={{ x: ['0%', '-50%'] }}
          transition={{
            duration: 15,
            ease: 'linear',
            repeat: Infinity,
            repeatType: 'loop',
          }}
          style={{ width: 'calc(300%)' }}
        >
          {logos.concat(logos).map((logo, index) => (
            <img
              key={index}
              src={logo}
              alt={`Logo cliente ${index}`}
              className="w-[15rem]"
            />
          ))}
        </motion.div>
      </div>
    </Section>
  )
})
