import { forwardRef } from 'react'
import ReCAP<PERSON><PERSON> from 'react-google-recaptcha'
import { InputText } from '@thrift/design-system/packages/molecules/InputText'
import { InputEmail } from '@thrift/design-system/packages/molecules/InputEmail'
import { InputPhone } from '@thrift/design-system/packages/molecules/InputPhone'
import { InputTextArea } from '@thrift/design-system/packages/molecules/InputTextArea'
import { Button } from '@thrift/design-system/packages/molecules/Button'
import { Section } from '@app/pages/Home/components/Section'

export const Contact = forwardRef<HTMLElement>((props, ref) => {
  return (
    <Section
      id="contact"
      title="Pronto para simplificar a gestão da sua empresa?"
      subtitle="Preencha o formulário abaixo e vamos conversar sobre como a Thrift Technology pode te ajudar."
      bgColor="bg-white"
      ref={ref}
      {...props}
    >
      <div className="flex flex-col h-full gap-6 w-full max-w-xl mx-auto">
        <InputText label="Seu nome" />
        <InputEmail label="E-mail" />
        <InputPhone label="Celular" />
        <InputTextArea label="Mensagem" />
        <ReCAPTCHA
          sitekey="YOUR_SITE_KEY" // Replace with your actual Site Key
        />
      </div>
      <div className="w-full flex justify-center mt-10">
        <div className="w-full max-w-xl flex flex-col">
          <Button variant="primary">Enviar</Button>
        </div>
      </div>
    </Section>
  )
})
