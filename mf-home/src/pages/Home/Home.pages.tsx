import { useRef } from 'react'
import { Header } from '@app/pages/Home/sections/Header'
import { Banner } from '@app/pages/Home/sections/Banner'
import { ClientsCarousel } from '@app/pages/Home/sections/ClientsCarousel'
import { Features } from '@app/pages/Home/sections/Features'
import { Pricing } from '@app/pages/Home/sections/Pricing'
import { Contact } from '@app/pages/Home/sections/Contact'
import { Footer } from '@app/pages/Home/sections/Footer'

export const Home = () => {
  const homeRef = useRef<HTMLElement>(null)
  const contactRef = useRef<HTMLElement>(null)
  const pricingRef = useRef<HTMLElement>(null)
  const featuresRef = useRef<HTMLElement>(null)
  const footerRef = useRef<HTMLElement>(null)
  const clientsRef = useRef<HTMLElement>(null)

  const scrollTo = (ref: React.RefObject<HTMLElement | null>) => {
    ref?.current?.scrollIntoView({ behavior: 'smooth' })
  }

  return (
    <div className="flex flex-col h-screen">
      <Header
        onHomeClick={() => scrollTo(homeRef)}
        onFeaturesClick={() => scrollTo(featuresRef)}
        onPlansClick={() => scrollTo(pricingRef)}
        onContactClick={() => scrollTo(contactRef)}
      />
      <main className="flex-1 overflow-y-auto ">
        <Banner onContactClick={() => scrollTo(contactRef)} />
        <ClientsCarousel ref={clientsRef} />
        <Features ref={featuresRef} />
        <Pricing ref={pricingRef} />
        <Contact ref={contactRef} />
        <Footer ref={footerRef} />
      </main>
    </div>
  )
}
