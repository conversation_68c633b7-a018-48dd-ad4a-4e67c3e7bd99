{"info": {"_postman_id": "838766-0947e5d6-86e4-47e6-9b8f-dc11a3c57d9c", "name": "Person", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "item": [{"name": "Person", "item": [{"name": "/person", "id": "838766-e60c7a11-1708-41c7-91ff-8eb02b54dc6f", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/person-natural/", "host": ["{{baseUrl}}"], "path": ["person-natural", ""]}}, "response": []}, {"name": "/person/:personId", "id": "838766-47080231-ecd6-4ccb-8d43-6cbc8708f159", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/person/:personId", "host": ["{{baseUrl}}"], "path": ["person", ":personId"], "variable": [{"id": "1cbdf2e3-ba10-43a6-8f71-9e3d5de51c46", "key": "personId", "value": ""}]}}, "response": []}, {"name": "/person", "id": "838766-a7c7aae9-aa0d-4d26-aed4-e27ec26f8b9a", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "OPTIONS", "header": [], "url": {"raw": "{{baseUrl}}/person", "host": ["{{baseUrl}}"], "path": ["person"]}}, "response": []}, {"name": "/person/:personId", "id": "838766-8e5cd951-d2c9-408d-9cd2-8979d9658762", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "OPTIONS", "header": [], "url": {"raw": "{{baseUrl}}/person/:personId", "host": ["{{baseUrl}}"], "path": ["person", ":personId"], "variable": [{"id": "fdfab0da-05b5-4fd6-8316-5cc1655a5ea2", "key": "personId", "value": ""}]}}, "response": []}], "id": "838766-817cce86-5781-42f4-9c8a-98a5dd9f5629"}, {"name": "Person Natural", "item": [{"name": "/person-natural", "id": "838766-414ae0e2-f042-4f8c-ba69-54c88e93de21", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/person-natural", "host": ["{{baseUrl}}"], "path": ["person-natural"]}}, "response": []}, {"name": "/person-natural/:id", "id": "838766-3e8a04a3-25b2-441c-b841-042ba8ce2d4d", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/person-natural/:id", "host": ["{{baseUrl}}"], "path": ["person-natural", ":id"], "variable": [{"key": "id", "value": "1acc188e-5b2b-4945-a7db-b8d02a0d9fe8"}]}}, "response": []}, {"name": "/person-natural/personId/:personId", "id": "838766-38a4fefc-7ee1-4c0b-9576-b3f70cb80372", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/person-natural/personId/:personId", "host": ["{{baseUrl}}"], "path": ["person-natural", "personId", ":personId"], "variable": [{"key": "personId", "value": "48fdd4a8-2e86-40b9-aca3-49cc99a972d9"}]}}, "response": []}, {"name": "/person-natural", "id": "838766-303c6b31-313a-45b4-b9a0-7b1d80ac7c52", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\n    \"firstName\": \"<PERSON><PERSON>\",\n    \"lastName\": \"<PERSON><PERSON>\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{baseUrl}}/person-natural", "host": ["{{baseUrl}}"], "path": ["person-natural"]}}, "response": []}, {"name": "/person-natural/:personId", "id": "838766-70f2346e-59c4-4d2f-9fcc-6cd689cf77b4", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "PUT", "header": [], "body": {"mode": "raw", "raw": "{\n    \"name\": \"<PERSON>\",\n    \"nickname\": \"<PERSON>\",\n    \"birthDate\": \"1990-01-01\",\n    \"gender\": \"male\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{baseUrl}}/person-natural/:personId", "host": ["{{baseUrl}}"], "path": ["person-natural", ":personId"], "variable": [{"key": "personId", "value": ""}]}}, "response": []}, {"name": "/person-natural/:id", "id": "838766-0a208199-67af-4431-a451-cb95efd2ee29", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "DELETE", "header": [], "url": {"raw": "{{baseUrl}}/person-natural/:id", "host": ["{{baseUrl}}"], "path": ["person-natural", ":id"], "variable": [{"key": "id", "value": ""}]}}, "response": []}, {"name": "/person-natural", "id": "838766-1da29812-aefd-47cc-ab98-8e822cde2409", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "OPTIONS", "header": [], "url": {"raw": "{{baseUrl}}/person-natural", "host": ["{{baseUrl}}"], "path": ["person-natural"]}}, "response": []}, {"name": "/person-natural/:id", "id": "838766-a976e3e9-2b1f-44be-8e40-0d937fe94388", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "OPTIONS", "header": [], "url": {"raw": "{{baseUrl}}/person-natural/:id", "host": ["{{baseUrl}}"], "path": ["person-natural", ":id"], "variable": [{"key": "id", "value": ""}]}}, "response": []}, {"name": "/person-natural/personId/:personId", "id": "838766-3db9af92-96d8-4c97-b723-26c2be09e419", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "OPTIONS", "header": [], "url": {"raw": "{{baseUrl}}/person-natural/personId/:personId", "host": ["{{baseUrl}}"], "path": ["person-natural", "personId", ":personId"], "variable": [{"key": "personId", "value": ""}]}}, "response": []}], "id": "838766-239d90b6-5e69-4e32-8e22-f043b7cec480"}, {"name": "Person Legal", "item": [{"name": "/person-legal", "id": "838766-599d2707-8578-4e7b-95aa-7b630ea8c8dd", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/person-legal", "host": ["{{baseUrl}}"], "path": ["person-legal"]}}, "response": []}, {"name": "/person-legal/:id", "id": "838766-8d4aded9-8bb9-4647-ba42-850db917c7bf", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/person-legal/:id", "host": ["{{baseUrl}}"], "path": ["person-legal", ":id"], "variable": [{"key": "id", "value": ""}]}}, "response": []}, {"name": "/person-legal/personId/:personId", "id": "838766-2cbd5149-c96e-42c3-9bd6-bf23055a705d", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/person-legal/:personId", "host": ["{{baseUrl}}"], "path": ["person-legal", ":personId"], "variable": [{"key": "personId", "value": "123e4567-e89b-12d3-a456-426614174000"}]}}, "response": []}, {"name": "/person-legal", "id": "838766-3f162712-c953-4d37-8ef9-00e2390c13c2", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\n    \"fictitiousName\": \"Company Fictitious Name\",\n    \"legalName\": \"Company Legal Name Inc.\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{baseUrl}}/person-legal", "host": ["{{baseUrl}}"], "path": ["person-legal"]}}, "response": []}, {"name": "/person-legal/:personId", "id": "838766-b608c189-8055-4c7e-8242-81a975358c87", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "PUT", "header": [], "body": {"mode": "raw", "raw": "{\n    \"fictitiousName\": \"Updated Fictitious Name\",\n    \"legalName\": \"Updated Legal Name Inc.\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{baseUrl}}/person/legal/:personId", "host": ["{{baseUrl}}"], "path": ["person", "legal", ":personId"], "variable": [{"key": "personId", "value": ""}]}}, "response": []}, {"name": "/person-legal/:id", "id": "838766-13f68290-0571-4470-8bce-cc017d2df99d", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "DELETE", "header": [], "url": {"raw": "{{baseUrl}}/person/legal/:id", "host": ["{{baseUrl}}"], "path": ["person", "legal", ":id"], "variable": [{"key": "id", "value": ""}]}}, "response": []}, {"name": "/person-legal", "id": "838766-37f9c35e-849f-4f71-b9eb-aa9b8c477d26", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "OPTIONS", "header": [], "url": {"raw": "{{baseUrl}}/person/legal", "host": ["{{baseUrl}}"], "path": ["person", "legal"]}}, "response": []}, {"name": "/person-legal/:id", "id": "838766-7d150a66-ae54-4da3-a849-a06d14bf558a", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "OPTIONS", "header": [], "url": {"raw": "{{baseUrl}}/person/legal/:id", "host": ["{{baseUrl}}"], "path": ["person", "legal", ":id"], "variable": [{"key": "id", "value": ""}]}}, "response": []}, {"name": "/person-legal/personId/:personId", "id": "838766-a6340c05-7a39-4025-a23e-e8af893c0b77", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "OPTIONS", "header": [], "url": {"raw": "{{baseUrl}}/person/legal/personId/:personId", "host": ["{{baseUrl}}"], "path": ["person", "legal", "personId", ":personId"], "variable": [{"key": "personId", "value": ""}]}}, "response": []}], "id": "838766-7b9a39e0-726c-46b9-b507-a6ad3dc3647f"}, {"name": "Store Credit", "item": [{"name": "/:personId/store-credits", "id": "838766-3c98b3d2-39ac-4bc0-8ab8-ce08b97d1c2e", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/:personId/store-credits", "host": ["{{baseUrl}}"], "path": [":personId", "store-credits"], "variable": [{"key": "personId", "value": "c01e168c-25c7-41b3-af0b-3d34ea672903"}]}}, "response": []}, {"name": "/:personId/store-credits/balance", "id": "838766-d617a6e0-0bf9-4e40-bced-2f43c8ea0b55", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/:personId/store-credits/balance", "host": ["{{baseUrl}}"], "path": [":personId", "store-credits", "balance"], "variable": [{"key": "personId", "value": "c01e168c-25c7-41b3-af0b-3d34ea672903"}]}}, "response": []}, {"name": "/:personId/store-credits", "id": "838766-dc1ae39f-213f-495a-99aa-e0a3bcc7aef0", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"amount\": 100.50,\n  \"currency\": \"BRL\",\n  \"issuingStoreLegalPersonId\": \"store-uuid-issuer\",\n  \"reason\": \"Devolução de produto\",\n  \"expiresAt\": \"2025-12-31T23:59:59.000Z\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{baseUrl}}/:personId/store-credits", "host": ["{{baseUrl}}"], "path": [":personId", "store-credits"], "variable": [{"key": "personId", "value": "c01e168c-25c7-41b3-af0b-3d34ea672903"}]}}, "response": []}, {"name": "/:personId/store-credits/use", "id": "838766-31843686-7e67-44a3-8b8b-c4a96a2750b5", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"amountToUse\": 50.25,\n  \"currency\": \"BRL\",\n  \"storeLegalPersonId\": \"f47ac10b-58cc-4372-a567-0e02b2c3d479\",\n  \"reason\": \"Compra de novo item\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{baseUrl}}/:personId/store-credits/use", "host": ["{{baseUrl}}"], "path": [":personId", "store-credits", "use"], "variable": [{"key": "personId", "value": "550e8400-e29b-41d4-a716-446655440000"}]}}, "response": []}], "id": "838766-37463fcc-4dbf-49ab-a0d6-a5c5a21d706c"}, {"name": "Contact", "item": [{"name": "/contact", "id": "838766-b5143f8b-1f4f-4e12-8327-435093bc3ce0", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/contact", "host": ["{{baseUrl}}"], "path": ["contact"]}}, "response": []}, {"name": "/contact/:id", "id": "838766-bfd80c02-430e-472f-95bd-52a529a00f2c", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/contact/:id", "host": ["{{baseUrl}}"], "path": ["contact", ":id"], "variable": [{"id": "a05a76ef-2ecc-4482-be93-221ba9e216c7", "key": "id", "value": ""}]}}, "response": []}, {"name": "/contact/personId/:personId", "id": "838766-8e16bab7-d0b5-45af-9d91-e4546c1e70ed", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/contact/personId/:personId", "host": ["{{baseUrl}}"], "path": ["contact", "personId", ":personId"], "variable": [{"key": "personId", "value": ""}]}}, "response": []}, {"name": "/contact", "id": "838766-10193d63-3089-4b94-92cd-219728a25c57", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\n    \"personId\": \"{{personId}}\",\n    \"email\": \"<EMAIL>\",\n    \"phone\": \"+1234567890\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{baseUrl}}/contact", "host": ["{{baseUrl}}"], "path": ["contact"]}}, "response": []}, {"name": "/contact/:id", "id": "838766-45e098ae-bdac-40a7-84f5-ba1824040717", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "PUT", "header": [], "body": {"mode": "raw", "raw": "{\n    \"email\": \"<EMAIL>\",\n    \"phone\": \"+0987654321\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{baseUrl}}/contact/:id", "host": ["{{baseUrl}}"], "path": ["contact", ":id"], "variable": [{"id": "392a796c-6775-487a-8714-eacdbdeb70ae", "key": "id", "value": ""}]}}, "response": []}, {"name": "/contact/:personId", "id": "838766-e6a8377c-4d8b-485a-bf01-5b3b15a9f40a", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "DELETE", "header": [], "url": {"raw": "{{baseUrl}}/contact/:personId", "host": ["{{baseUrl}}"], "path": ["contact", ":personId"], "variable": [{"id": "a63d0afc-26c4-44b2-839c-b85bea452846", "key": "personId", "value": ""}]}}, "response": []}, {"name": "/contact", "id": "838766-bd48242b-b035-4acb-97f3-30c68dab5224", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "OPTIONS", "header": [], "url": {"raw": "{{baseUrl}}/contact", "host": ["{{baseUrl}}"], "path": ["contact"]}}, "response": []}, {"name": "/contact/:id", "id": "838766-22cf8d68-3244-402b-a916-ddac4bd93303", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "OPTIONS", "header": [], "url": {"raw": "{{baseUrl}}/contact/:id", "host": ["{{baseUrl}}"], "path": ["contact", ":id"], "variable": [{"id": "9753b9f5-3957-44b6-89cc-ac89b1d36421", "key": "id", "value": ""}]}}, "response": []}, {"name": "/contact/personId/:personId", "id": "838766-08702ee4-1498-4e87-80e9-efe080758c8a", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "OPTIONS", "header": [], "url": {"raw": "{{baseUrl}}/contact/personId/:personId", "host": ["{{baseUrl}}"], "path": ["contact", "personId", ":personId"], "variable": [{"id": "10e3789c-aa43-46b1-b749-653a2bb54aa8", "key": "personId", "value": ""}]}}, "response": []}], "id": "838766-c05f6881-ee61-4b0d-9900-30db59f0d570"}, {"name": "Address", "item": [{"name": "/address", "id": "838766-5f0fb8ed-3692-4e76-a024-7491fd6d50c5", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/address", "host": ["{{baseUrl}}"], "path": ["address"]}}, "response": []}, {"name": "/address/:id", "id": "838766-85d44edf-4c54-45af-b912-f8e7262c837d", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/address/:id", "host": ["{{baseUrl}}"], "path": ["address", ":id"], "variable": [{"id": "1cdbe8ad-d215-45f0-b701-ba03074ec440", "key": "id", "value": ""}]}}, "response": []}, {"name": "/address/personId/:personId", "id": "838766-5741ca2a-20db-4f89-b8fc-8416d472adee", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/address/personId/:personId", "host": ["{{baseUrl}}"], "path": ["address", "personId", ":personId"], "variable": [{"id": "ae45eb57-b057-4936-bb5a-25f6c6d6aaf1", "key": "personId", "value": ""}]}}, "response": []}, {"name": "/address", "id": "838766-129e5b51-70c6-49ba-bac5-bfc5dd66167a", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\n    \"personId\": \"{{personId}}\",\n    \"street\": \"123 Main St\",\n    \"city\": \"Anytown\",\n    \"state\": \"ST\",\n    \"zipCode\": \"12345\",\n    \"country\": \"Country\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{baseUrl}}/address", "host": ["{{baseUrl}}"], "path": ["address"]}}, "response": []}, {"name": "/address/:id", "id": "838766-ccc6b932-5c83-48b5-8a69-08010cc1856b", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "PUT", "header": [], "body": {"mode": "raw", "raw": "{\n    \"street\": \"456 New St\",\n    \"city\": \"Newtown\",\n    \"state\": \"NS\",\n    \"zipCode\": \"54321\",\n    \"country\": \"New Country\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{baseUrl}}/address/:id", "host": ["{{baseUrl}}"], "path": ["address", ":id"], "variable": [{"id": "5d2ddb86-bd49-481d-b8d3-a781f438a9d1", "key": "id", "value": ""}]}}, "response": []}, {"name": "/address/:personId", "id": "838766-f6d4861b-43f4-47d8-ac68-d3d864224b89", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "DELETE", "header": [], "url": {"raw": "{{baseUrl}}/address/:personId", "host": ["{{baseUrl}}"], "path": ["address", ":personId"], "variable": [{"id": "6fbc635c-2f1e-45e8-a038-421d49afd501", "key": "personId", "value": ""}]}}, "response": []}, {"name": "/address", "id": "838766-33041219-f7e8-4418-8646-472649bfe3a1", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "OPTIONS", "header": [], "url": {"raw": "{{baseUrl}}/address", "host": ["{{baseUrl}}"], "path": ["address"]}}, "response": []}, {"name": "/address/:id", "id": "838766-6f3ac5d6-f655-486f-9cce-777bd1997a47", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "OPTIONS", "header": [], "url": {"raw": "{{baseUrl}}/address/:id", "host": ["{{baseUrl}}"], "path": ["address", ":id"], "variable": [{"id": "bf8c8459-e512-4fa5-9235-3e698ee411fc", "key": "id", "value": ""}]}}, "response": []}, {"name": "/address/personId/:personId", "id": "838766-a03671f1-18be-40d1-a733-d5f37f17b1c5", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "OPTIONS", "header": [], "url": {"raw": "{{baseUrl}}/address/personId/:personId", "host": ["{{baseUrl}}"], "path": ["address", "personId", ":personId"], "variable": [{"id": "e5661edc-e2e3-4716-a156-0ccccc00b29b", "key": "personId", "value": ""}]}}, "response": []}], "id": "838766-4955c98b-2ded-4fb4-b041-e3519a9ff1b9"}, {"name": "Document", "item": [{"name": "/document", "id": "838766-a820b258-ee16-4a44-9ff4-cef8b196d1d7", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/document", "host": ["{{baseUrl}}"], "path": ["document"]}}, "response": []}, {"name": "/document/:id", "id": "838766-948e8a51-a227-4ad8-b5ed-f1443e658fc1", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/document/:id", "host": ["{{baseUrl}}"], "path": ["document", ":id"], "variable": [{"id": "b5189729-007f-48b7-9afe-880c5fdedd6f", "key": "id", "value": ""}]}}, "response": []}, {"name": "/document/personId/:personId", "id": "838766-758624b8-6f74-49fc-8451-ad5dd483df93", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/document/personId/:personId", "host": ["{{baseUrl}}"], "path": ["document", "personId", ":personId"], "variable": [{"id": "c46f283b-9cda-411c-97d6-372d2789c7a9", "key": "personId", "value": ""}]}}, "response": []}, {"name": "/document", "id": "838766-fcc9d551-6feb-4abe-af95-bbb31ee42761", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\n    \"personId\": \"{{personId}}\",\n    \"type\": \"ID\",\n    \"number\": \"123456789\",\n    \"issuedAt\": \"2020-01-01\",\n    \"expiresAt\": \"2030-01-01\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{baseUrl}}/document", "host": ["{{baseUrl}}"], "path": ["document"]}}, "response": []}, {"name": "/document/:id", "id": "838766-fb5cc01a-bf79-4b38-91ed-8364eef223ee", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "PUT", "header": [], "body": {"mode": "raw", "raw": "{\n    \"type\": \"ID\",\n    \"number\": \"987654321\",\n    \"issuedAt\": \"2020-01-01\",\n    \"expiresAt\": \"2030-01-01\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{baseUrl}}/document/:id", "host": ["{{baseUrl}}"], "path": ["document", ":id"], "variable": [{"id": "19a4baf8-7cd4-4d6e-b600-a295f593eacd", "key": "id", "value": ""}]}}, "response": []}, {"name": "/document/:personId", "id": "838766-01b95495-70e0-4826-8254-3c1ef5b8d859", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "DELETE", "header": [], "url": {"raw": "{{baseUrl}}/document/:personId", "host": ["{{baseUrl}}"], "path": ["document", ":personId"], "variable": [{"id": "4c514ec7-7509-4096-b0d7-aef9bb488309", "key": "personId", "value": ""}]}}, "response": []}, {"name": "/document", "id": "838766-dab5ce23-0ba9-4cd9-83cc-d2d55f082eb3", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "OPTIONS", "header": [], "url": {"raw": "{{baseUrl}}/document", "host": ["{{baseUrl}}"], "path": ["document"]}}, "response": []}, {"name": "/document/:id", "id": "838766-6c1262e4-c2da-4769-a44a-9df66b3a06c4", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "OPTIONS", "header": [], "url": {"raw": "{{baseUrl}}/document/:id", "host": ["{{baseUrl}}"], "path": ["document", ":id"], "variable": [{"id": "5c1385ae-3cb0-4312-b638-df12ad98e02d", "key": "id", "value": ""}]}}, "response": []}, {"name": "/document/personId/:personId", "id": "838766-2f401b79-abba-4d45-a2fe-5e76c30b2c45", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "OPTIONS", "header": [], "url": {"raw": "{{baseUrl}}/document/personId/:personId", "host": ["{{baseUrl}}"], "path": ["document", "personId", ":personId"], "variable": [{"id": "01db611a-1bff-4506-b64c-ab2c910a64f9", "key": "personId", "value": ""}]}}, "response": []}], "id": "838766-5ac761a5-7545-4134-8e55-8491e8618edb"}], "auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{token}}", "type": "string"}]}, "event": [{"listen": "prerequest", "script": {"type": "text/javascript", "packages": {}, "exec": [""], "id": "80ef6d3c-309a-4209-b0c9-7931a5133ed1"}}, {"listen": "test", "script": {"type": "text/javascript", "packages": {}, "exec": [""], "id": "a667a438-0e94-4dff-8142-b2219674e130"}}], "variable": [{"key": "baseUrl", "value": "http://localhost:8081", "type": "string"}]}