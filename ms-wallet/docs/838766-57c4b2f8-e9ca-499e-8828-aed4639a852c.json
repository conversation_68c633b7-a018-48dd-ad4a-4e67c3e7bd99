{"info": {"_postman_id": "838766-57c4b2f8-e9ca-499e-8828-aed4639a852c", "name": "Wallet", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "item": [{"name": "Tenant", "item": [{"name": "/tenants/:id", "id": "838766-4c2c3d3a-1bcc-4deb-ae19-d59fa0ad2e3c", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "OPTIONS", "header": [], "url": {"raw": "{{baseUrl}}/tenants/:id", "host": ["{{baseUrl}}"], "path": ["tenants", ":id"], "variable": [{"id": "3b3cabf8-e4fd-4b8a-82e1-b3070172dcef", "key": "id", "value": ""}]}}, "response": []}, {"name": "/tenants", "id": "838766-d4377e6a-102b-4f7f-bcc6-42f35e8805c3", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "OPTIONS", "header": [], "url": {"raw": "{{baseUrl}}/tenants", "host": ["{{baseUrl}}"], "path": ["tenants"]}}, "response": []}, {"name": "/tenants/:id", "id": "838766-bea84282-ff42-41dd-aeaa-7082784404d2", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/tenants/:id", "host": ["{{baseUrl}}"], "path": ["tenants", ":id"], "variable": [{"id": "10354e53-711f-46ef-aa9f-83a160ee981e", "key": "id", "value": ""}]}}, "response": []}, {"name": "/tenants", "id": "838766-e906e3e1-f086-4572-9fb8-af7e34ae934f", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/tenants", "host": ["{{baseUrl}}"], "path": ["tenants"]}}, "response": []}, {"name": "/tenants/:id/verify", "id": "838766-68c565f9-d4ff-4fc0-8e9e-191f2fc78f1d", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"auth": {"type": "<PERSON><PERSON><PERSON>"}, "method": "HEAD", "header": [], "url": {"raw": "{{baseUrl}}/tenants/:id/verify", "host": ["{{baseUrl}}"], "path": ["tenants", ":id", "verify"], "variable": [{"key": "id", "value": "c597c17c-b077-4fd7-a3b1-516b2541dceb"}]}}, "response": []}, {"name": "/tenants/name/:name", "id": "838766-2dbe6ae4-0a12-479b-b898-5643f327bb5b", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"auth": {"type": "<PERSON><PERSON><PERSON>"}, "method": "HEAD", "header": [], "url": {"raw": "{{baseUrl}}/tenants/name/:name", "host": ["{{baseUrl}}"], "path": ["tenants", "name", ":name"], "variable": [{"key": "name", "value": "thrift"}]}}, "response": []}, {"name": "/tenants/domain/:domain", "id": "838766-3a396d71-5ad9-4470-8d55-210675d1da5e", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"auth": {"type": "<PERSON><PERSON><PERSON>"}, "method": "HEAD", "header": [], "url": {"raw": "{{baseUrl}}/tenants/domain/:domain", "host": ["{{baseUrl}}"], "path": ["tenants", "domain", ":domain"], "variable": [{"key": "domain", "value": "thrift-dev.com"}]}}, "response": []}, {"name": "/tenants", "id": "838766-0fe78e44-c01c-4e2e-9996-1199e71c0928", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\n    \"name\": \"Example Tenant\",\n    \"tenantUid\": \"example-tenant\",\n    \"balance\": 1000.00\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{baseUrl}}/tenants", "host": ["{{baseUrl}}"], "path": ["tenants"]}}, "response": []}, {"name": "/tenants/:id", "id": "838766-a496109d-9ec5-4450-ac6b-eaf381ea4a2f", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "PUT", "header": [], "body": {"mode": "raw", "raw": "{\n    \"name\": \"Updated Tenant\",\n    \"tenantUid\": \"updated-tenant\",\n    \"balance\": 2000.00\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{baseUrl}}/tenants/:id", "host": ["{{baseUrl}}"], "path": ["tenants", ":id"], "variable": [{"id": "f6b4067a-8d6a-47de-9688-501fd4169f98", "key": "id", "value": ""}]}}, "response": []}, {"name": "/tenants/:id", "id": "838766-044a3bbe-26e1-4ac9-9501-1f7f4696727d", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "DELETE", "header": [], "url": {"raw": "{{baseUrl}}/tenants/:id", "host": ["{{baseUrl}}"], "path": ["tenants", ":id"], "variable": [{"id": "884c70ab-7b69-4b7a-9fdc-fe7a9d126944", "key": "id", "value": ""}]}}, "response": []}], "id": "838766-c13a027a-f04c-4367-8c75-69b23af4dab5"}, {"name": "Tenant Contact", "item": [{"name": "/tenant-contacts/:id", "id": "838766-d486501c-326c-461e-bbeb-929ac02f5aa4", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "OPTIONS", "header": [], "url": {"raw": "{{baseUrl}}/tenant-contacts/:id", "host": ["{{baseUrl}}"], "path": ["tenant-contacts", ":id"], "variable": [{"id": "d673a774-59b2-4e00-8c7c-d96ebd6734c8", "key": "id", "value": ""}]}}, "response": []}, {"name": "/tenant-contacts", "id": "838766-884a32ed-25b2-4a0e-ab0d-1c67941a619f", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "OPTIONS", "header": [], "url": {"raw": "{{baseUrl}}/tenant-contacts", "host": ["{{baseUrl}}"], "path": ["tenant-contacts"]}}, "response": []}, {"name": "/tenant-contacts/:id", "id": "838766-c5719314-9a4a-436c-9d8e-b1a015f2f0ee", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/tenant-contacts/:id", "host": ["{{baseUrl}}"], "path": ["tenant-contacts", ":id"], "variable": [{"id": "1e4d8386-d95b-4d63-8073-2d9b627ed271", "key": "id", "value": ""}]}}, "response": []}, {"name": "/tenant-contacts", "id": "838766-2706d2dd-fa7f-4b71-8c3f-89380a115353", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/tenant-contacts?id={{id}}&tenantId={{tenantId}}&page=1&perPage=10", "host": ["{{baseUrl}}"], "path": ["tenant-contacts"], "query": [{"key": "id", "value": "{{id}}"}, {"key": "tenantId", "value": "{{tenantId}}"}, {"key": "page", "value": "1"}, {"key": "perPage", "value": "10"}]}}, "response": []}, {"name": "/tenant-contacts", "id": "838766-f69159f0-a85f-4fb4-9ddd-bf8a20888cc6", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\n    \"tenantId\": \"{{tenantId}}\",\n    \"type\": \"email\",\n    \"value\": \"<EMAIL>\",\n    \"isPrimary\": true\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{baseUrl}}/tenant-contacts", "host": ["{{baseUrl}}"], "path": ["tenant-contacts"]}}, "response": []}, {"name": "/tenant-contacts/:id", "id": "838766-ba2590fe-f3bc-4eea-a62e-099e3b67baa8", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "PUT", "header": [], "body": {"mode": "raw", "raw": "{\n    \"type\": \"email\",\n    \"value\": \"<EMAIL>\",\n    \"isPrimary\": true\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{baseUrl}}/tenant-contacts/:id", "host": ["{{baseUrl}}"], "path": ["tenant-contacts", ":id"], "variable": [{"id": "fb75bb8f-6cda-45d6-9880-ae9f62df3ea8", "key": "id", "value": ""}]}}, "response": []}, {"name": "/tenant-contacts/:id", "id": "838766-ad9d9fe1-13ca-4730-98de-a552e072d13e", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "DELETE", "header": [], "url": {"raw": "{{baseUrl}}/tenant-contacts/:id", "host": ["{{baseUrl}}"], "path": ["tenant-contacts", ":id"], "variable": [{"id": "2dc3d660-0264-452d-ba72-a636ef2bb22b", "key": "id", "value": ""}]}}, "response": []}], "id": "838766-ade8f922-96d5-4cb1-abe7-6d1d49de80c9"}, {"name": "Tenant Document", "item": [{"name": "/tenant-documents/:id", "id": "838766-152757d9-b486-449a-9ff7-8c8e587fba47", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "OPTIONS", "header": [], "url": {"raw": "{{baseUrl}}/tenant-documents/:id", "host": ["{{baseUrl}}"], "path": ["tenant-documents", ":id"], "variable": [{"id": "8dce4cda-3058-40a7-836b-5d9c3fa5be2b", "key": "id", "value": ""}]}}, "response": []}, {"name": "/tenant-documents", "id": "838766-f8864422-e03a-4515-9834-8b97e8602b5d", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "OPTIONS", "header": [], "url": {"raw": "{{baseUrl}}/tenant-documents", "host": ["{{baseUrl}}"], "path": ["tenant-documents"]}}, "response": []}, {"name": "/tenant-documents/:id", "id": "838766-47896148-8f49-4fc1-a448-9c24d59c83e7", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/tenant-documents/:id", "host": ["{{baseUrl}}"], "path": ["tenant-documents", ":id"], "variable": [{"id": "ca24b671-4994-43c0-86e3-8eb3a34a694b", "key": "id", "value": ""}]}}, "response": []}, {"name": "/tenant-documents", "id": "838766-6f730990-31eb-4611-b41c-588351b953e5", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/tenant-documents?id={{id}}&tenantId={{tenantId}}&page=1&perPage=10", "host": ["{{baseUrl}}"], "path": ["tenant-documents"], "query": [{"key": "id", "value": "{{id}}"}, {"key": "tenantId", "value": "{{tenantId}}"}, {"key": "page", "value": "1"}, {"key": "perPage", "value": "10"}]}}, "response": []}, {"name": "/tenant-documents", "id": "838766-d759b661-3363-43ff-a85c-6ba5a49a012d", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\n    \"tenantId\": \"{{tenantId}}\",\n    \"type\": \"CNPJ\",\n    \"value\": \"12345678000190\",\n    \"issuedAt\": \"2020-01-01T00:00:00.000Z\",\n    \"isPrimary\": true\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{baseUrl}}/tenant-documents", "host": ["{{baseUrl}}"], "path": ["tenant-documents"]}}, "response": []}, {"name": "/tenant-documents/:id", "id": "838766-62c10d1d-829b-497d-b5d4-c336ff39eebe", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "PUT", "header": [], "body": {"mode": "raw", "raw": "{\n    \"type\": \"CNPJ\",\n    \"value\": \"98765432000190\",\n    \"issuedAt\": \"2020-01-01T00:00:00.000Z\",\n    \"expiresAt\": \"2025-01-01T00:00:00.000Z\",\n    \"isPrimary\": true\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{baseUrl}}/tenant-documents/:id", "host": ["{{baseUrl}}"], "path": ["tenant-documents", ":id"], "variable": [{"id": "ab8e5ff1-90b8-4a22-897d-a55ac855d34e", "key": "id", "value": ""}]}}, "response": []}, {"name": "/tenant-documents/:id", "id": "838766-347b77d1-7da7-4082-9f36-c078e41312ee", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "DELETE", "header": [], "url": {"raw": "{{baseUrl}}/tenant-documents/:id", "host": ["{{baseUrl}}"], "path": ["tenant-documents", ":id"], "variable": [{"id": "19f1102b-7a4c-46e1-8cc9-0f27b56baec5", "key": "id", "value": ""}]}}, "response": []}], "id": "838766-47905def-c3d0-4ff7-8fd9-9da72114a771"}, {"name": "Domain", "item": [{"name": "/domains/:id", "id": "838766-c22ee634-aaad-43dd-b64c-fdf5eb3fcf2a", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "OPTIONS", "header": [], "url": {"raw": "{{baseUrl}}/domains/:id", "host": ["{{baseUrl}}"], "path": ["domains", ":id"], "variable": [{"id": "6aa36f70-8b47-4cd2-b007-7362bb024fc5", "key": "id", "value": ""}]}}, "response": []}, {"name": "/domains", "id": "838766-50a18237-f433-444e-8442-e517e155ffdd", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "OPTIONS", "header": [], "url": {"raw": "{{baseUrl}}/domains", "host": ["{{baseUrl}}"], "path": ["domains"]}}, "response": []}, {"name": "/domains/:id", "id": "838766-da497011-a825-4df1-83b2-4ac3ff6c7001", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/domains/:id", "host": ["{{baseUrl}}"], "path": ["domains", ":id"], "variable": [{"id": "e4c02449-4744-40f1-bb9b-ed1ab8c32060", "key": "id", "value": ""}]}}, "response": []}, {"name": "/domains", "id": "838766-68d62c07-9640-4dcf-bb25-b73df4a036b0", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/domains", "host": ["{{baseUrl}}"], "path": ["domains"]}}, "response": []}, {"name": "/domains", "id": "838766-0414e390-e927-4ace-8ca9-852c8d480656", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\n    \"name\": \"Example Domain\",\n    \"tenantId\": \"uuid\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{baseUrl}}/domains", "host": ["{{baseUrl}}"], "path": ["domains"]}}, "response": []}, {"name": "/domains/:id", "id": "838766-1c6c1c1a-d8fa-478e-89a3-d52add7e3897", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "PUT", "header": [], "body": {"mode": "raw", "raw": "{\n    \"name\": \"Updated Domain\",\n    \"tenantId\": \"uuid\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{baseUrl}}/domains/:id", "host": ["{{baseUrl}}"], "path": ["domains", ":id"], "variable": [{"id": "0797f50f-6cea-4ca1-8b3e-f7cd2482b48f", "key": "id", "value": ""}]}}, "response": []}, {"name": "/domains/:id", "id": "838766-1d0f6104-3289-428a-8494-2d62adf768b5", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "DELETE", "header": [], "url": {"raw": "{{baseUrl}}/domains/:id", "host": ["{{baseUrl}}"], "path": ["domains", ":id"], "variable": [{"id": "1fbb580c-1b38-476f-95cb-98ad82ce35b7", "key": "id", "value": ""}]}}, "response": []}], "id": "838766-b18c2ccc-53b5-46a8-befc-9418c0304f0e"}, {"name": "Payment Method", "item": [{"name": "/payment-methods/:id", "id": "838766-83b68cf3-3eb3-46f0-9549-00e3cff10d4f", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "OPTIONS", "header": [], "url": {"raw": "{{baseUrl}}/payment-methods/:id", "host": ["{{baseUrl}}"], "path": ["payment-methods", ":id"], "variable": [{"id": "9a20920d-99ee-4f66-8bde-99e769de6e8a", "key": "id", "value": ""}]}}, "response": []}, {"name": "/payment-methods", "id": "838766-2de1fb90-f73c-4743-ad8d-9fb394f40a83", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "OPTIONS", "header": [], "url": {"raw": "{{baseUrl}}/payment-methods", "host": ["{{baseUrl}}"], "path": ["payment-methods"]}}, "response": []}, {"name": "/payment-methods/:id", "id": "838766-7cd919b1-2474-4e7e-8b72-c88a8fa4e217", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/payment-methods/:id", "host": ["{{baseUrl}}"], "path": ["payment-methods", ":id"], "variable": [{"id": "6e396aff-f59d-4bb3-89a0-c427ff373cd3", "key": "id", "value": ""}]}}, "response": []}, {"name": "/payment-methods", "id": "838766-b20188f9-9588-4cf3-9934-a3b65b11e9d5", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/payment-methods?tenantId={{tenantId}}&provider={{provider}}&enabled={{enabled}}&page=1&perPage=10", "host": ["{{baseUrl}}"], "path": ["payment-methods"], "query": [{"key": "tenantId", "value": "{{tenantId}}"}, {"key": "provider", "value": "{{provider}}"}, {"key": "enabled", "value": "{{enabled}}"}, {"key": "page", "value": "1"}, {"key": "perPage", "value": "10"}]}}, "response": []}, {"name": "/payment-methods", "id": "838766-40f1ad56-5418-4169-83ac-5da9de1154f9", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\n    \"tenantId\": \"{{tenantId}}\",\n    \"provider\": \"stripe\",\n    \"config\": {\n        \"apiKey\": \"sk_test_example\",\n        \"webhookSecret\": \"whsec_example\"\n    },\n    \"enabled\": true\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{baseUrl}}/payment-methods", "host": ["{{baseUrl}}"], "path": ["payment-methods"]}}, "response": []}, {"name": "/payment-methods/:id", "id": "838766-30a8100a-5974-48d2-853d-bfd27e237d62", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "PUT", "header": [], "body": {"mode": "raw", "raw": "{\n    \"provider\": \"stripe\",\n    \"config\": {\n        \"apiKey\": \"sk_test_updated\",\n        \"webhookSecret\": \"whsec_updated\"\n    },\n    \"enabled\": true\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{baseUrl}}/payment-methods/:id", "host": ["{{baseUrl}}"], "path": ["payment-methods", ":id"], "variable": [{"id": "e111ece3-1ad2-4456-bfcf-5f5462f91827", "key": "id", "value": ""}]}}, "response": []}, {"name": "/payment-methods/:id", "id": "838766-574cab23-4207-45fa-b3b1-2d6e745fac2a", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "DELETE", "header": [], "url": {"raw": "{{baseUrl}}/payment-methods/:id", "host": ["{{baseUrl}}"], "path": ["payment-methods", ":id"], "variable": [{"id": "6d3347bc-58cb-4f2c-b1de-d5eee2c2c06f", "key": "id", "value": ""}]}}, "response": []}], "id": "838766-011309b5-b63c-4a07-ac59-ef21a5db4626"}, {"name": "Payment History", "item": [{"name": "/payment-history/:id", "id": "838766-e42048e8-6d42-4b03-9a7a-82cedc78f25a", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "OPTIONS", "header": [], "url": {"raw": "{{baseUrl}}/payment-history/:id", "host": ["{{baseUrl}}"], "path": ["payment-history", ":id"], "variable": [{"id": "157d3f29-c8ce-470b-98cf-cb41cda7fc9e", "key": "id", "value": ""}]}}, "response": []}, {"name": "/payment-history", "id": "838766-dacfb4ae-0630-4e63-b367-30795f48511d", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "OPTIONS", "header": [], "url": {"raw": "{{baseUrl}}/payment-history", "host": ["{{baseUrl}}"], "path": ["payment-history"]}}, "response": []}, {"name": "/payment-history/:id", "id": "838766-6bfdd520-3c60-4ab9-9c9f-4d3ced8920db", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/payment-history/:id", "host": ["{{baseUrl}}"], "path": ["payment-history", ":id"], "variable": [{"id": "91ce382c-592e-49e0-a01f-8cccf36852c7", "key": "id", "value": ""}]}}, "response": []}, {"name": "/payment-history", "id": "838766-f77d0cf6-044e-482d-a0ab-b942c7b73d8b", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/payment-history?tenantId={{tenantId}}&paymentMethodId={{paymentMethodId}}&status={{status}}&fromDate={{fromDate}}&toDate={{toDate}}&page=1&perPage=10", "host": ["{{baseUrl}}"], "path": ["payment-history"], "query": [{"key": "tenantId", "value": "{{tenantId}}"}, {"key": "paymentMethodId", "value": "{{paymentMethodId}}"}, {"key": "status", "value": "{{status}}"}, {"key": "fromDate", "value": "{{fromDate}}"}, {"key": "toDate", "value": "{{toDate}}"}, {"key": "page", "value": "1"}, {"key": "perPage", "value": "10"}]}}, "response": []}, {"name": "/payment-history", "id": "838766-1fa22e17-8ddb-4555-9259-c10b7b44fb83", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\n    \"tenantId\": \"{{tenantId}}\",\n    \"paymentMethodId\": \"{{paymentMethodId}}\",\n    \"amount\": 99.99,\n    \"currency\": \"USD\",\n    \"status\": \"completed\",\n    \"externalId\": \"ch_123456789\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{baseUrl}}/payment-history", "host": ["{{baseUrl}}"], "path": ["payment-history"]}}, "response": []}, {"name": "/payment-history/:id", "id": "838766-0a81fdf1-f0b1-4bfc-a283-6413df505c4b", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "PUT", "header": [], "body": {"mode": "raw", "raw": "{\n    \"paymentMethodId\": \"{{paymentMethodId}}\",\n    \"amount\": 149.99,\n    \"currency\": \"USD\",\n    \"status\": \"refunded\",\n    \"externalId\": \"ch_updated_123456789\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{baseUrl}}/payment-history/:id", "host": ["{{baseUrl}}"], "path": ["payment-history", ":id"], "variable": [{"id": "8201bfd7-6d1d-4708-90c5-81625ec58b0c", "key": "id", "value": ""}]}}, "response": []}, {"name": "/payment-history/:id", "id": "838766-f4448711-8265-44b4-b0be-3227f0beb76b", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "DELETE", "header": [], "url": {"raw": "{{baseUrl}}/payment-history/:id", "host": ["{{baseUrl}}"], "path": ["payment-history", ":id"], "variable": [{"id": "a6693ccb-c8b3-4fdc-b8ed-6f8d61a2ac4f", "key": "id", "value": ""}]}}, "response": []}], "id": "838766-d6024fd8-a2e2-481e-8804-134c1012b109"}], "auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{token}}", "type": "string"}]}, "event": [{"listen": "prerequest", "script": {"id": "8b7a9293-3aa6-498d-9ca1-7929901849fb", "type": "text/javascript", "packages": {}, "exec": [""]}}, {"listen": "test", "script": {"id": "d83e518e-53d1-45ad-a246-2e2579d5a8d9", "type": "text/javascript", "packages": {}, "exec": [""]}}], "variable": [{"key": "baseUrl", "value": "{{defaultBaseUrl}}/wallet", "type": "string"}]}