model ProductGridAttribute {
  id            String   @id @default(uuid())
  name          String
  productGridId String
  createdAt     DateTime @default(now())
  updatedAt     DateTime @updatedAt
  deleted       <PERSON>olean  @default(false)

  productGrid ProductGrid                    @relation(fields: [productGridId], references: [id])
  variations  ProductGridAttributeVariation[]

  @@unique([name, productGridId])
  @@map("product_grid_attributes")
}
