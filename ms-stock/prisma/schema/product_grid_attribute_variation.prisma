model ProductGridAttributeVariation {
  id                     String   @id @default(uuid())
  name                   String
  productGridAttributeId String
  createdAt              DateTime @default(now())
  updatedAt              DateTime @updatedAt
  deleted                Boolean  @default(false)

  productGridAttribute ProductGridAttribute @relation(fields: [productGridAttributeId], references: [id])

  @@unique([name, productGridAttributeId])
  @@map("product_grid_attribute_variations")
}
