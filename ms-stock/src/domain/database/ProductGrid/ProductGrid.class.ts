import { PrismaClient } from '@prisma/client'
import {
  type AllowedLanguages,
  LanguageFactory,
} from '@thrift/i18n/engines/Language'
import configs from 'app.config.json'

import NotFoundException from '@thrift/common/engines/Resource/exceptions/NotFoundException'

import type {
  CreateProductGridDto,
  FetchProductGridsProps,
  ProductGridModel,
  ProductGridWithAttributes,
  UpdateProductGridDto,
} from '@app/domain/database/ProductGrid'

const i18next = LanguageFactory({
  fallbackLng: configs.server.language as AllowedLanguages,
})

export class ProductGrid {
  private prisma: PrismaClient

  constructor() {
    this.prisma = new PrismaClient()
  }

  public async findById(id: string): Promise<ProductGridWithAttributes> {
    const productGrid = await this.prisma.productGrid.findFirst({
      where: { id, deleted: false },
      include: {
        attributes: {
          where: { deleted: false },
          select: {
            id: true,
            name: true,
          },
        },
      },
    })

    if (!productGrid) {
      throw new NotFoundException(
        i18next.t('common:notFound', { name: 'product grid' }),
      )
    }

    return productGrid
  }

  public async findByName(name: string): Promise<ProductGridModel | null> {
    return this.prisma.productGrid.findFirst({
      where: { name, deleted: false },
    })
  }

  public async find(params: FetchProductGridsProps) {
    const {
      page = 1,
      perPage = 20,
      sortBy = 'name',
      sortOrder = 'asc',
      search,
    } = params

    const where = {
      deleted: false,
      ...(search && {
        OR: [
          { name: { contains: search } },
          { description: { contains: search } },
        ],
      }),
    }

    const [items, total] = await Promise.all([
      this.prisma.productGrid.findMany({
        where,
        include: {
          attributes: {
            where: { deleted: false },
            select: {
              id: true,
              name: true,
            },
          },
        },
        orderBy: { [sortBy]: sortOrder },
        skip: (page - 1) * perPage,
        take: perPage,
      }),
      this.prisma.productGrid.count({ where }),
    ])

    return {
      items,
      totalPages: Math.ceil(total / perPage),
    }
  }

  public async create(dto: CreateProductGridDto): Promise<ProductGridModel> {
    return this.prisma.productGrid.create({
      data: {
        name: dto.name,
        description: dto.description || null,
      },
    })
  }

  public async update(dto: UpdateProductGridDto): Promise<ProductGridModel> {
    return this.prisma.productGrid.update({
      where: { id: dto.id },
      data: {
        ...(dto.name !== undefined && { name: dto.name }),
        ...(dto.description !== undefined && { description: dto.description }),
      },
    })
  }

  public async delete(id: string): Promise<void> {
    await this.prisma.productGrid.update({
      where: { id },
      data: { deleted: true },
    })
  }
}
