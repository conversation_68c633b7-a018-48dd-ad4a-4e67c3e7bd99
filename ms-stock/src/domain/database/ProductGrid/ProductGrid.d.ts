export interface ProductGridModel {
  id: string
  name: string
  description: string | null
  createdAt: Date
  updatedAt: Date
  deleted: boolean
}

export interface CreateProductGridDto {
  name: string
  description?: string
}

export interface UpdateProductGridDto {
  id: string
  name?: string
  description?: string
}

export interface FetchProductGridsProps {
  page?: number
  perPage?: number
  sortBy?: string
  sortOrder?: 'asc' | 'desc'
  search?: string
}

export interface ProductGridWithAttributes {
  id: string
  name: string
  description: string | null
  attributes: {
    id: string
    name: string
  }[]
}
