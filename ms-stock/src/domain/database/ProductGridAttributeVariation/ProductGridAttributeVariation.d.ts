export interface ProductGridAttributeVariationModel {
  id: string
  name: string
  productGridAttributeId: string
  createdAt: Date
  updatedAt: Date
  deleted: boolean
}

export interface CreateProductGridAttributeVariationDto {
  name: string
  productGridAttributeId: string
}

export interface UpdateProductGridAttributeVariationDto {
  id: string
  name?: string
}

export interface FetchProductGridAttributeVariationsProps {
  productGridAttributeId: string
  page?: number
  perPage?: number
  sortBy?: string
  sortOrder?: 'asc' | 'desc'
  search?: string
}
