import { PrismaClient } from '@prisma/client'
import {
  type AllowedLanguages,
  LanguageFactory,
} from '@thrift/i18n/engines/Language'
import configs from 'app.config.json'

import NotFoundException from '@thrift/common/engines/Resource/exceptions/NotFoundException'

import type {
  CreateProductGridAttributeVariationDto,
  FetchProductGridAttributeVariationsProps,
  ProductGridAttributeVariationModel,
  UpdateProductGridAttributeVariationDto,
} from '@app/domain/database/ProductGridAttributeVariation'

const i18next = LanguageFactory({
  fallbackLng: configs.server.language as AllowedLanguages,
})

export class ProductGridAttributeVariation {
  private prisma: PrismaClient

  constructor() {
    this.prisma = new PrismaClient()
  }

  public async findById(
    id: string,
  ): Promise<ProductGridAttributeVariationModel> {
    const variation = await this.prisma.productGridAttributeVariation.findFirst(
      {
        where: { id, deleted: false },
      },
    )

    if (!variation) {
      throw new NotFoundException(
        i18next.t('common:notFound', {
          name: 'product grid attribute variation',
        }),
      )
    }

    return variation
  }

  public async findByNameAndAttributeId(
    name: string,
    productGridAttributeId: string,
  ): Promise<ProductGridAttributeVariationModel | null> {
    return this.prisma.productGridAttributeVariation.findFirst({
      where: { name, productGridAttributeId, deleted: false },
    })
  }

  public async findByProductGridAttributeId(
    params: FetchProductGridAttributeVariationsProps,
  ) {
    const {
      productGridAttributeId,
      page = 1,
      perPage = 20,
      sortBy = 'name',
      sortOrder = 'asc',
      search,
    } = params

    const where = {
      productGridAttributeId,
      deleted: false,
      ...(search && {
        name: { contains: search },
      }),
    }

    const [items, total] = await Promise.all([
      this.prisma.productGridAttributeVariation.findMany({
        where,
        orderBy: { [sortBy]: sortOrder },
        skip: (page - 1) * perPage,
        take: perPage,
      }),
      this.prisma.productGridAttributeVariation.count({ where }),
    ])

    return {
      items,
      totalPages: Math.ceil(total / perPage),
    }
  }

  public async create(
    dto: CreateProductGridAttributeVariationDto,
  ): Promise<ProductGridAttributeVariationModel> {
    return this.prisma.productGridAttributeVariation.create({
      data: {
        name: dto.name,
        productGridAttributeId: dto.productGridAttributeId,
      },
    })
  }

  public async update(
    dto: UpdateProductGridAttributeVariationDto,
  ): Promise<ProductGridAttributeVariationModel> {
    return this.prisma.productGridAttributeVariation.update({
      where: { id: dto.id },
      data: {
        ...(dto.name !== undefined && { name: dto.name }),
      },
    })
  }

  public async delete(id: string): Promise<void> {
    await this.prisma.productGridAttributeVariation.update({
      where: { id },
      data: { deleted: true },
    })
  }
}
