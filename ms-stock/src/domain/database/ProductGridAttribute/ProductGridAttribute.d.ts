export interface ProductGridAttributeModel {
  id: string
  name: string
  productGridId: string
  createdAt: Date
  updatedAt: Date
  deleted: boolean
}

export interface CreateProductGridAttributeDto {
  name: string
  productGridId: string
}

export interface UpdateProductGridAttributeDto {
  id: string
  name?: string
}

export interface FetchProductGridAttributesProps {
  productGridId: string
  page?: number
  perPage?: number
  sortBy?: string
  sortOrder?: 'asc' | 'desc'
  search?: string
}

export interface ProductGridAttributeWithVariations {
  id: string
  name: string
  productGridId: string
  variations: {
    id: string
    name: string
  }[]
}
