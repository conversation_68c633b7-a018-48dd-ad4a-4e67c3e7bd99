import { PrismaClient } from '@prisma/client'
import {
  type AllowedLanguages,
  LanguageFactory,
} from '@thrift/i18n/engines/Language'
import configs from 'app.config.json'

import NotFoundException from '@thrift/common/engines/Resource/exceptions/NotFoundException'

import type {
  CreateProductGridAttributeDto,
  FetchProductGridAttributesProps,
  ProductGridAttributeModel,
  ProductGridAttributeWithVariations,
  UpdateProductGridAttributeDto,
} from '@app/domain/database/ProductGridAttribute'

const i18next = LanguageFactory({
  fallbackLng: configs.server.language as AllowedLanguages,
})

export class ProductGridAttribute {
  private prisma: PrismaClient

  constructor() {
    this.prisma = new PrismaClient()
  }

  public async findById(
    id: string,
  ): Promise<ProductGridAttributeWithVariations> {
    const attribute = await this.prisma.productGridAttribute.findFirst({
      where: { id, deleted: false },
      include: {
        variations: {
          where: { deleted: false },
          select: {
            id: true,
            name: true,
          },
        },
      },
    })

    if (!attribute) {
      throw new NotFoundException(
        i18next.t('common:notFound', { name: 'product grid attribute' }),
      )
    }

    return attribute
  }

  public async findByNameAndProductGridId(
    name: string,
    productGridId: string,
  ): Promise<ProductGridAttributeModel | null> {
    return this.prisma.productGridAttribute.findFirst({
      where: { name, productGridId, deleted: false },
    })
  }

  public async findByProductGridId(params: FetchProductGridAttributesProps) {
    const {
      productGridId,
      page = 1,
      perPage = 20,
      sortBy = 'name',
      sortOrder = 'asc',
      search,
    } = params

    const where = {
      productGridId,
      deleted: false,
      ...(search && {
        name: { contains: search },
      }),
    }

    const [items, total] = await Promise.all([
      this.prisma.productGridAttribute.findMany({
        where,
        include: {
          variations: {
            where: { deleted: false },
            select: {
              id: true,
              name: true,
            },
          },
        },
        orderBy: { [sortBy]: sortOrder },
        skip: (page - 1) * perPage,
        take: perPage,
      }),
      this.prisma.productGridAttribute.count({ where }),
    ])

    return {
      items,
      totalPages: Math.ceil(total / perPage),
    }
  }

  public async create(
    dto: CreateProductGridAttributeDto,
  ): Promise<ProductGridAttributeModel> {
    return this.prisma.productGridAttribute.create({
      data: {
        name: dto.name,
        productGridId: dto.productGridId,
      },
    })
  }

  public async update(
    dto: UpdateProductGridAttributeDto,
  ): Promise<ProductGridAttributeModel> {
    return this.prisma.productGridAttribute.update({
      where: { id: dto.id },
      data: {
        ...(dto.name !== undefined && { name: dto.name }),
      },
    })
  }

  public async delete(id: string): Promise<void> {
    await this.prisma.productGridAttribute.update({
      where: { id },
      data: { deleted: true },
    })
  }
}
