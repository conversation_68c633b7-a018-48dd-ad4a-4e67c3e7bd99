import { PrismaClient } from '@prisma/client'
import {
  type AllowedLanguages,
  LanguageFactory,
} from '@thrift/i18n/engines/Language'
import configs from 'app.config.json'

import NotFoundException from '@thrift/common/engines/Resource/exceptions/NotFoundException'

import type {
  CreateTagDto,
  FetchTagsProps,
  UpdateTagDto,
} from '@app/domain/database/Tag'

const i18next = LanguageFactory({
  fallbackLng: configs.server.language as AllowedLanguages,
})

export class Tag {
  private prisma: PrismaClient

  constructor() {
    this.prisma = new PrismaClient()
  }

  public async findById(id: string) {
    const tag = await this.prisma.tag.findFirst({
      where: { id, deleted: false },
      select: {
        id: true,
        name: true,
      },
    })

    if (!tag) {
      throw new NotFoundException(i18next.t('common:notFound', { name: 'tag' }))
    }

    return tag
  }

  public async findByName(name: string) {
    return this.prisma.tag.findFirst({
      where: { name, deleted: false },
      select: {
        id: true,
        name: true,
      },
    })
  }

  public async find({
    page = 1,
    perPage = 10,
    search,
    sortBy = 'id',
    sortOrder = 'asc',
  }: FetchTagsProps) {
    const skip = (page - 1) * +perPage

    const where = {
      deleted: false,
      ...(search && {
        OR: [{ name: { contains: search } }],
      }),
    }

    const [tag, totalItems] = await this.prisma.$transaction([
      this.prisma.tag.findMany({
        where,
        skip,
        take: +perPage,
        orderBy: { [sortBy]: sortOrder },
        select: {
          id: true,
          name: true,
        },
      }),
      this.prisma.tag.count({ where }),
    ])

    return {
      items: tag,
      totalPages: Math.ceil(totalItems / +perPage),
    }
  }

  public async create({ name }: CreateTagDto) {
    const tag = await this.prisma.tag.create({
      data: {
        name,
      },
      select: {
        id: true,
        name: true,
      },
    })

    return tag
  }

  public async update({ id, name }: UpdateTagDto) {
    const tag = await this.prisma.tag.update({
      where: { id },
      data: {
        ...(name && { name }),
      },
      select: {
        id: true,
        name: true,
      },
    })

    return tag
  }

  public async delete(id: string) {
    const tag = await this.prisma.tag.update({
      where: { id },
      data: { deleted: true },
    })

    return tag
  }
}
