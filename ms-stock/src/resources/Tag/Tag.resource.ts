import type { PaginationParams } from '@thrift/common/engines/Pagination'
import {
  ResourceMessageCode,
  type ResourceMethodProps,
} from '@thrift/common/engines/Resource'
import { ResponseHelper } from '@thrift/common/engines/Resource/helpers/ResponseHelper'
import { Delete, Get, Post, Put } from '@thrift/common/engines/Server'

import { Language } from '@app/application/Language'
import { Create, Delete as DeleteTag, Read, Update } from '@app/application/Tag'

import type {
  IdentifierDto,
  PostTagDto,
  PutTagDto,
} from '@app/resources/Tag/Tag'

export class TagResource {
  @Get('/tags/:id')
  public async getTagById({ request, response }: ResourceMethodProps) {
    try {
      const { id } = request.params<IdentifierDto>()

      response.send({
        code: ResourceMessageCode.C_200_0200,
        message: Language.translate(
          `resources:${ResourceMessageCode.C_200_0200}`,
        ),
        data: await new Read().findById(id),
      })
    } catch (error) {
      ResponseHelper.handleError(error, response, 'tag', Language.translate)
    }
  }

  @Get('/tags')
  public async getTags({ request, response }: ResourceMethodProps) {
    try {
      const paginationParams = request.query<PaginationParams>()

      const result = await new Read().find(paginationParams)

      response.send({
        code: ResourceMessageCode.C_200_0200,
        message: Language.translate(
          `resources:${ResourceMessageCode.C_200_0200}`,
          {
            title: 'tag',
          },
        ),
        data: result.items,
      })
    } catch (error) {
      ResponseHelper.handleError(error, response, 'tag', Language.translate)
    }
  }

  @Post('/tags')
  public async postTag({ request, response }: ResourceMethodProps) {
    try {
      const postTagDto = request.body<PostTagDto>()

      response.send({
        code: ResourceMessageCode.C_201_0001,
        message: Language.translate(
          `resources:${ResourceMessageCode.C_201_0001}`,
          {
            title: 'tag',
          },
        ),
        data: await new Create().create(postTagDto),
      })
    } catch (error) {
      ResponseHelper.handleError(error, response, 'tag', Language.translate)
    }
  }

  @Put('/tags/:id')
  public async updateTag({ request, response }: ResourceMethodProps) {
    try {
      const { id } = request.params<IdentifierDto>()
      const updateTagDto = request.body<PutTagDto>()

      response.send({
        code: ResourceMessageCode.C_200_0001,
        message: Language.translate(
          `resources:${ResourceMessageCode.C_200_0001}`,
          {
            title: 'tag',
          },
        ),
        data: await new Update().update({
          id,
          ...updateTagDto,
        }),
      })
    } catch (error) {
      ResponseHelper.handleError(error, response, 'tag', Language.translate)
    }
  }

  @Delete('/tags/:id')
  public async deleteTag({ request, response }: ResourceMethodProps) {
    try {
      const { id } = request.params<IdentifierDto>()

      await new DeleteTag().delete(id)

      response.send({
        code: ResourceMessageCode.C_200_0001,
        message: Language.translate(
          `resources:${ResourceMessageCode.C_204_0001}`,
          {
            title: 'tag',
          },
        ),
      })
    } catch (error) {
      ResponseHelper.handleError(error, response, 'tag', Language.translate)
    }
  }
}
