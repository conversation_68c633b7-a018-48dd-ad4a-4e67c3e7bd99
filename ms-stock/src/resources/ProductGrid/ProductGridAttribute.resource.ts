import type { PaginationParams } from '@thrift/common/engines/Pagination'
import {
  ResourceMessageCode,
  type ResourceMethodProps,
} from '@thrift/common/engines/Resource'
import { ResponseHelper } from '@thrift/common/engines/Resource/helpers/ResponseHelper'
import { Delete, Get, Post, Put } from '@thrift/common/engines/Server'

import { Language } from '@app/application/Language'
import {
  Create as CreateProductGridAttribute,
  Delete as DeleteProductGridAttribute,
  Read as ReadProductGridAttribute,
  Update as UpdateProductGridAttribute,
} from '@app/application/ProductGridAttribute'

import type {
  PostProductGridAttributeDto,
  ProductGridAttributeIdentifierDto,
  ProductGridIdentifierDto,
  PutProductGridAttributeDto,
} from '@app/resources/ProductGrid/ProductGrid'

export class ProductGridAttributeResource {
  @Get('/product-grid/:productGridId/attributes')
  public async getProductGridAttributesByProductGridId({
    request,
    response,
  }: ResourceMethodProps) {
    try {
      const { productGridId } = request.params<ProductGridIdentifierDto>()
      const paginationParams = request.query<PaginationParams>()

      const result = await new ReadProductGridAttribute().findByProductGridId({
        productGridId,
        ...paginationParams,
      })

      ResponseHelper.sendSuccessResponse(
        response,
        result.items,
        ResourceMessageCode.C_200_0200,
        Language.translate,
        { title: 'product grid attribute' },
      )
    } catch (error) {
      ResponseHelper.handleError(
        error,
        response,
        'product grid attribute',
        Language.translate,
      )
    }
  }

  @Get('/product-grid/:productGridId/attributes/:productGridAttributeId')
  public async getProductGridAttributeById({
    request,
    response,
  }: ResourceMethodProps) {
    try {
      const { productGridAttributeId } =
        request.params<ProductGridAttributeIdentifierDto>()

      ResponseHelper.sendSuccessResponse(
        response,
        await new ReadProductGridAttribute().findById(productGridAttributeId),
        ResourceMessageCode.C_200_0200,
        Language.translate,
      )
    } catch (error) {
      ResponseHelper.handleError(
        error,
        response,
        'product grid attribute',
        Language.translate,
      )
    }
  }

  @Post('/product-grid/:productGridId/attributes')
  public async createProductGridAttribute({
    request,
    response,
  }: ResourceMethodProps) {
    try {
      const { productGridId } = request.params<ProductGridIdentifierDto>()
      const { name } = request.body<PostProductGridAttributeDto>()

      const data = await new CreateProductGridAttribute().create({
        productGridId,
        name,
      })

      ResponseHelper.sendSuccessResponse(
        response,
        data,
        ResourceMessageCode.C_201_0001,
        Language.translate,
        { title: 'product grid attribute' },
      )
    } catch (error) {
      ResponseHelper.handleError(
        error,
        response,
        'product grid attribute',
        Language.translate,
      )
    }
  }

  @Put('/product-grid/:productGridId/attributes/:productGridAttributeId')
  public async updateProductGridAttribute({
    request,
    response,
  }: ResourceMethodProps) {
    try {
      const { productGridAttributeId } =
        request.params<ProductGridAttributeIdentifierDto>()

      const updateData = request.body<PutProductGridAttributeDto>()

      const data = await new UpdateProductGridAttribute().update({
        id: productGridAttributeId,
        ...updateData,
      })

      ResponseHelper.sendSuccessResponse(
        response,
        data,
        ResourceMessageCode.C_200_0001,
        Language.translate,
        { title: 'product grid attribute' },
      )
    } catch (error) {
      ResponseHelper.handleError(
        error,
        response,
        'product grid attribute',
        Language.translate,
      )
    }
  }

  @Delete('/product-grid/:productGridId/attributes/:productGridAttributeId')
  public async deleteProductGridAttribute({
    request,
    response,
  }: ResourceMethodProps) {
    try {
      const { productGridAttributeId } =
        request.params<ProductGridAttributeIdentifierDto>()

      await new DeleteProductGridAttribute().delete(productGridAttributeId)

      ResponseHelper.sendSuccessResponse(
        response,
        null,
        ResourceMessageCode.C_204_0001,
        Language.translate,
        { title: 'product grid attribute' },
      )
    } catch (error) {
      ResponseHelper.handleError(
        error,
        response,
        'product grid attribute',
        Language.translate,
      )
    }
  }
}
