import type { PaginationParams } from '@thrift/common/engines/Pagination'
import {
  ResourceMessageCode,
  type ResourceMethodProps,
} from '@thrift/common/engines/Resource'
import { ResponseHelper } from '@thrift/common/engines/Resource/helpers/ResponseHelper'
import { Delete, Get, Post, Put } from '@thrift/common/engines/Server'

import { Language } from '@app/application/Language'
import {
  Create,
  Delete as DeleteProductGrid,
  Read,
  Update,
} from '@app/application/ProductGrid'

import type {
  IdentifierDto,
  PutProductGridDto,
} from '@app/resources/ProductGrid/ProductGrid'

export class ProductGridResource {
  @Get('/product-grid/:id')
  public async getProductGridById({ request, response }: ResourceMethodProps) {
    try {
      const { id } = request.params<IdentifierDto>()

      ResponseHelper.sendSuccessResponse(
        response,
        await new Read().findById(id),
        ResourceMessageCode.C_200_0200,
        Language.translate,
      )
    } catch (error) {
      ResponseHelper.handleError(
        error,
        response,
        'product grid',
        Language.translate,
      )
    }
  }

  @Get('/product-grid')
  public async getProductGrids({ request, response }: ResourceMethodProps) {
    try {
      const paginationParams = request.query<PaginationParams>()

      const result = await new Read().find(paginationParams)

      ResponseHelper.sendSuccessResponse(
        response,
        result.items,
        ResourceMessageCode.C_200_0200,
        Language.translate,
        { title: 'product grid' },
      )
    } catch (error) {
      ResponseHelper.handleError(
        error,
        response,
        'product grid',
        Language.translate,
      )
    }
  }

  @Post('/product-grid')
  public async postProductGrid({ request, response }: ResourceMethodProps) {
    try {
      const data = await new Create().create(request.body())

      ResponseHelper.sendSuccessResponse(
        response,
        data,
        ResourceMessageCode.C_201_0001,
        Language.translate,
        { title: 'product grid' },
      )
    } catch (error) {
      ResponseHelper.handleError(
        error,
        response,
        'product grid',
        Language.translate,
      )
    }
  }

  @Put('/product-grid/:id')
  public async updateProductGrid({ request, response }: ResourceMethodProps) {
    try {
      const { id } = request.params<IdentifierDto>()
      const updateData = request.body<PutProductGridDto>()

      const data = await new Update().update({
        id,
        ...updateData,
      })

      ResponseHelper.sendSuccessResponse(
        response,
        data,
        ResourceMessageCode.C_200_0001,
        Language.translate,
        { title: 'product grid' },
      )
    } catch (error) {
      ResponseHelper.handleError(
        error,
        response,
        'product grid',
        Language.translate,
      )
    }
  }

  @Delete('/product-grid/:id')
  public async deleteProductGrid({ request, response }: ResourceMethodProps) {
    try {
      const { id } = request.params<IdentifierDto>()

      await new DeleteProductGrid().delete(id)

      ResponseHelper.sendSuccessResponse(
        response,
        null,
        ResourceMessageCode.C_204_0001,
        Language.translate,
        { title: 'product grid' },
      )
    } catch (error) {
      ResponseHelper.handleError(
        error,
        response,
        'product grid',
        Language.translate,
      )
    }
  }
}
