export interface IdentifierDto {
  id: string
}

export interface PostProductGridDto {
  name: string
  description?: string
}

export interface PutProductGridDto {
  name?: string
  description?: string
}

export interface ProductGridIdentifierDto {
  productGridId: string
}

export interface ProductGridAttributeIdentifierDto {
  productGridId: string
  productGridAttributeId: string
}

export interface PostProductGridAttributeDto {
  name: string
}

export interface PutProductGridAttributeDto {
  name?: string
}

export interface ProductGridAttributeVariationIdentifierDto {
  productGridId: string
  productGridAttributeId: string
  productGridAttributeVariationId: string
}

export interface PostProductGridAttributeVariationDto {
  name: string
}

export interface PutProductGridAttributeVariationDto {
  name?: string
}
