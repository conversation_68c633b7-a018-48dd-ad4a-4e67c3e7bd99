import type { PaginationParams } from '@thrift/common/engines/Pagination'
import {
  ResourceMessageCode,
  type ResourceMethodProps,
} from '@thrift/common/engines/Resource'
import { ResponseHelper } from '@thrift/common/engines/Resource/helpers/ResponseHelper'
import { Delete, Get, Post, Put } from '@thrift/common/engines/Server'

import { Language } from '@app/application/Language'
import {
  Create as CreateProductGridAttributeVariation,
  Delete as DeleteProductGridAttributeVariation,
  Read as ReadProductGridAttributeVariation,
  Update as UpdateProductGridAttributeVariation,
} from '@app/application/ProductGridAttributeVariation'

import type {
  PostProductGridAttributeVariationDto,
  ProductGridAttributeIdentifierDto,
  ProductGridAttributeVariationIdentifierDto,
  PutProductGridAttributeVariationDto,
} from '@app/resources/ProductGrid/ProductGrid'

export class ProductGridAttributeVariationResource {
  @Get(
    '/product-grid/:productGridId/attributes/:productGridAttributeId/variations',
  )
  public async getProductGridAttributeVariationsByAttributeId({
    request,
    response,
  }: ResourceMethodProps) {
    try {
      const { productGridAttributeId } =
        request.params<ProductGridAttributeIdentifierDto>()

      const paginationParams = request.query<PaginationParams>()

      const result =
        await new ReadProductGridAttributeVariation().findByProductGridAttributeId(
          {
            productGridAttributeId,
            ...paginationParams,
          },
        )

      ResponseHelper.sendSuccessResponse(
        response,
        result.items,
        ResourceMessageCode.C_200_0200,
        Language.translate,
        { title: 'product grid attribute variation' },
      )
    } catch (error) {
      ResponseHelper.handleError(
        error,
        response,
        'product grid attribute variation',
        Language.translate,
      )
    }
  }

  @Get(
    '/product-grid/:productGridId/attributes/:productGridAttributeId/variations/:productGridAttributeVariationId',
  )
  public async getProductGridAttributeVariationById({
    request,
    response,
  }: ResourceMethodProps) {
    try {
      const { productGridAttributeVariationId } =
        request.params<ProductGridAttributeVariationIdentifierDto>()

      ResponseHelper.sendSuccessResponse(
        response,
        await new ReadProductGridAttributeVariation().findById(
          productGridAttributeVariationId,
        ),
        ResourceMessageCode.C_200_0200,
        Language.translate,
      )
    } catch (error) {
      ResponseHelper.handleError(
        error,
        response,
        'product grid attribute variation',
        Language.translate,
      )
    }
  }

  @Post(
    '/product-grid/:productGridId/attributes/:productGridAttributeId/variations',
  )
  public async createProductGridAttributeVariation({
    request,
    response,
  }: ResourceMethodProps) {
    try {
      const { productGridAttributeId } =
        request.params<ProductGridAttributeIdentifierDto>()

      const { name } = request.body<PostProductGridAttributeVariationDto>()

      const data = await new CreateProductGridAttributeVariation().create({
        productGridAttributeId,
        name,
      })

      ResponseHelper.sendSuccessResponse(
        response,
        data,
        ResourceMessageCode.C_201_0001,
        Language.translate,
        { title: 'product grid attribute variation' },
      )
    } catch (error) {
      ResponseHelper.handleError(
        error,
        response,
        'product grid attribute variation',
        Language.translate,
      )
    }
  }

  @Put(
    '/product-grid/:productGridId/attributes/:productGridAttributeId/variations/:productGridAttributeVariationId',
  )
  public async updateProductGridAttributeVariation({
    request,
    response,
  }: ResourceMethodProps) {
    try {
      const { productGridAttributeVariationId } =
        request.params<ProductGridAttributeVariationIdentifierDto>()

      const updateData = request.body<PutProductGridAttributeVariationDto>()

      const data = await new UpdateProductGridAttributeVariation().update({
        id: productGridAttributeVariationId,
        ...updateData,
      })

      ResponseHelper.sendSuccessResponse(
        response,
        data,
        ResourceMessageCode.C_200_0001,
        Language.translate,
        { title: 'product grid attribute variation' },
      )
    } catch (error) {
      ResponseHelper.handleError(
        error,
        response,
        'product grid attribute variation',
        Language.translate,
      )
    }
  }

  @Delete(
    '/product-grid/:productGridId/attributes/:productGridAttributeId/variations/:productGridAttributeVariationId',
  )
  public async deleteProductGridAttributeVariation({
    request,
    response,
  }: ResourceMethodProps) {
    try {
      const { productGridAttributeVariationId } =
        request.params<ProductGridAttributeVariationIdentifierDto>()

      await new DeleteProductGridAttributeVariation().delete(
        productGridAttributeVariationId,
      )

      ResponseHelper.sendSuccessResponse(
        response,
        null,
        ResourceMessageCode.C_204_0001,
        Language.translate,
        { title: 'product grid attribute variation' },
      )
    } catch (error) {
      ResponseHelper.handleError(
        error,
        response,
        'product grid attribute variation',
        Language.translate,
      )
    }
  }
}
