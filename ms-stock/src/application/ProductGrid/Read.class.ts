import type { PaginationParams } from '@thrift/common/engines/Pagination'

import type { ProductGridWithAttributesResponse } from '@app/application/ProductGrid/ProductGrid'

import { ProductGrid } from '@app/domain/database/ProductGrid'

export class Read {
  public async findById(
    id: string,
  ): Promise<ProductGridWithAttributesResponse> {
    const model = new ProductGrid()

    return model.findById(id)
  }

  public async find(params: PaginationParams) {
    const model = new ProductGrid()

    return model.find(params)
  }
}
