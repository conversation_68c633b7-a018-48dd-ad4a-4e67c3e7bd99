import { Language } from '@app/application/Language'
import type { ProductGridResponse } from '@app/application/ProductGrid/ProductGrid'
import { CreateProductGridSchema } from '@app/application/ProductGrid/ProductGrid.schema'

import { ProductGrid } from '@app/domain/database/ProductGrid'

export class Create {
  public async create(input: unknown): Promise<ProductGridResponse> {
    const dto = CreateProductGridSchema.parse(input)

    const model = new ProductGrid()

    // Check if name already exists
    const existingProductGrid = await model.findByName(dto.name)

    if (existingProductGrid) {
      throw new ReferenceError(
        Language.translate('common:invalid', {
          name: 'product.grid.name',
        }),
      )
    }

    return model.create(dto)
  }
}
