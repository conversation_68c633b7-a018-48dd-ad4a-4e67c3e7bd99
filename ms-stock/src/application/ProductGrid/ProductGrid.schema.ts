import { z } from 'zod'

import { PaginationFilterSchema } from '@thrift/common/engines/Pagination'

export const ProductGridBaseSchema = z.object({
  name: z.string().min(1).max(100),
  description: z.string().max(500).optional(),
})

export const CreateProductGridSchema = ProductGridBaseSchema

export const UpdateProductGridSchema = ProductGridBaseSchema.extend({
  id: z.uuid(),
}).partial({ name: true, description: true })

export const FilterProductGridSchema = PaginationFilterSchema
