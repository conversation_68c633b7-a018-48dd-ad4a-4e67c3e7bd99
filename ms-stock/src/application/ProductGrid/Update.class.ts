import { Language } from '@app/application/Language'
import type { ProductGridResponse } from '@app/application/ProductGrid/ProductGrid'
import { UpdateProductGridSchema } from '@app/application/ProductGrid/ProductGrid.schema'

import { ProductGrid } from '@app/domain/database/ProductGrid'

export class Update {
  public async update(input: unknown): Promise<ProductGridResponse> {
    const dto = UpdateProductGridSchema.parse(input)

    const model = new ProductGrid()

    // Check if product grid exists
    await model.findById(dto.id)

    // Check if the name already exists (if name is being updated)
    if (dto.name) {
      const existingProductGrid = await model.findByName(dto.name)

      if (existingProductGrid && existingProductGrid.id !== dto.id) {
        throw new ReferenceError(
          Language.translate('common:invalid', {
            name: 'product.grid.name',
          }),
        )
      }
    }

    return model.update(dto)
  }
}
