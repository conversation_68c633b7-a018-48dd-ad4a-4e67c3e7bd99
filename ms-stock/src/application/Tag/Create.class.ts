import { Language } from '@app/application/Language'
import type { TagResponse } from '@app/application/Tag/Tag'
import { CreateTagSchema } from '@app/application/Tag/Tag.schema'

import { Tag } from '@app/domain/database/Tag'

export class Create {
  public async create(input: unknown): Promise<TagResponse> {
    const dto = CreateTagSchema.parse(input)

    const model = new Tag()

    // Check if the name already exists
    const existingTag = await model.findByName(dto.name)

    if (existingTag) {
      throw new ReferenceError(
        Language.translate('common:invalid', {
          name: 'tag.name',
        }),
      )
    }

    // Create the tag
    return model.create(dto)
  }
}
