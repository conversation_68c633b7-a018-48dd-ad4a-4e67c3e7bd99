import type { PaginationParams } from '@thrift/common/engines/Pagination'

import type { TagResponse } from '@app/application/Tag/Tag'

import { Tag } from '@app/domain/database/Tag'

export class Read {
  public async findById(id: string): Promise<TagResponse> {
    const model = new Tag()

    return model.findById(id)
  }

  public async find(params: PaginationParams) {
    const model = new Tag()

    return model.find(params)
  }
}
