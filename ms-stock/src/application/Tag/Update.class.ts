import { Language } from '@app/application/Language'
import type { TagResponse } from '@app/application/Tag/Tag'
import { UpdateTagSchema } from '@app/application/Tag/Tag.schema'

import { Tag } from '@app/domain/database/Tag'

export class Update {
  public async update(input: unknown): Promise<TagResponse> {
    const dto = UpdateTagSchema.parse(input)

    const model = new Tag()

    // Check if tag exists
    await model.findById(dto.id)

    // Check if the name already exists (if name is being updated)
    if (dto.name) {
      const existingTag = await model.findByName(dto.name)

      if (existingTag && existingTag.id !== dto.id) {
        throw new ReferenceError(
          Language.translate('common:invalid', {
            name: 'tag.name',
          }),
        )
      }
    }

    // Update the tag
    return model.update(dto)
  }
}
