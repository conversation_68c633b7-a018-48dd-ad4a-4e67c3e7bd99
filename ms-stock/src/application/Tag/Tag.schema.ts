import { z } from 'zod'

import { createFilterSchema } from '@thrift/common/engines/Pagination'

export const TagBaseSchema = z.object({
  name: z.string().min(1).max(50),
})

export const CreateTagSchema = TagBaseSchema

export const UpdateTagSchema = TagBaseSchema.extend({
  id: z.uuid(),
}).partial({
  name: true,
})

export const FilterTagSchema = createFilterSchema({
  name: z.string().optional(),
})
