import type { ProductGridAttributeVariationResponse } from '@app/application/ProductGridAttributeVariation/ProductGridAttributeVariation'

import {
  type FetchProductGridAttributeVariationsProps,
  ProductGridAttributeVariation,
} from '@app/domain/database/ProductGridAttributeVariation'

export class Read {
  public async findById(
    id: string,
  ): Promise<ProductGridAttributeVariationResponse> {
    const model = new ProductGridAttributeVariation()

    return model.findById(id)
  }

  public async findByProductGridAttributeId(
    params: FetchProductGridAttributeVariationsProps,
  ) {
    const model = new ProductGridAttributeVariation()

    return model.findByProductGridAttributeId(params)
  }
}
