import { Language } from '@app/application/Language'
import type { ProductGridAttributeVariationResponse } from '@app/application/ProductGridAttributeVariation/ProductGridAttributeVariation'
import { UpdateProductGridAttributeVariationSchema } from '@app/application/ProductGridAttributeVariation/ProductGridAttributeVariation.schema'

import { ProductGridAttributeVariation } from '@app/domain/database/ProductGridAttributeVariation'

export class Update {
  public async update(
    input: unknown,
  ): Promise<ProductGridAttributeVariationResponse> {
    const dto = UpdateProductGridAttributeVariationSchema.parse(input)

    const model = new ProductGridAttributeVariation()

    // Check if variation exists and get its productGridAttributeId
    const existingVariation = await model.findById(dto.id)

    // Check if the name already exists for this attribute (if name is being updated)
    if (dto.name) {
      const duplicateVariation = await model.findByNameAndAttributeId(
        dto.name,
        existingVariation.productGridAttributeId,
      )

      if (duplicateVariation && duplicateVariation.id !== dto.id) {
        throw new ReferenceError(
          Language.translate('common:invalid', {
            name: 'product.grid.attribute.variation.name',
          }),
        )
      }
    }

    return model.update(dto)
  }
}
