import { z } from 'zod'

import { PaginationFilterSchema } from '@thrift/common/engines/Pagination'

export const ProductGridAttributeVariationBaseSchema = z.object({
  name: z.string().min(1).max(100),
})

export const CreateProductGridAttributeVariationSchema =
  ProductGridAttributeVariationBaseSchema.extend({
    productGridAttributeId: z.uuid(),
  })

export const UpdateProductGridAttributeVariationSchema =
  ProductGridAttributeVariationBaseSchema.extend({
    id: z.uuid(),
  }).partial({ name: true })

export const FilterProductGridAttributeVariationSchema =
  PaginationFilterSchema.extend({
    productGridAttributeId: z.uuid(),
  })
