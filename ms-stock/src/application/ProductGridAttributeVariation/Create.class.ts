import { Language } from '@app/application/Language'
import type { ProductGridAttributeVariationResponse } from '@app/application/ProductGridAttributeVariation/ProductGridAttributeVariation'
import { CreateProductGridAttributeVariationSchema } from '@app/application/ProductGridAttributeVariation/ProductGridAttributeVariation.schema'

import { ProductGridAttribute } from '@app/domain/database/ProductGridAttribute'
import { ProductGridAttributeVariation } from '@app/domain/database/ProductGridAttributeVariation'

export class Create {
  public async create(
    input: unknown,
  ): Promise<ProductGridAttributeVariationResponse> {
    const dto = CreateProductGridAttributeVariationSchema.parse(input)

    // Check if product grid attribute exists
    const attributeModel = new ProductGridAttribute()

    await attributeModel.findById(dto.productGridAttributeId)

    const model = new ProductGridAttributeVariation()

    // Check if variation name already exists for this attribute
    const existingVariation = await model.findByNameAndAttributeId(
      dto.name,
      dto.productGridAttributeId,
    )

    if (existingVariation) {
      throw new ReferenceError(
        Language.translate('common:invalid', {
          name: 'product.grid.attribute.variation.name',
        }),
      )
    }

    return model.create(dto)
  }
}
