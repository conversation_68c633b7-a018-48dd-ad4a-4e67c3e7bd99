import { Language } from '@app/application/Language'
import type { ProductGridAttributeResponse } from '@app/application/ProductGridAttribute/ProductGridAttribute'
import { UpdateProductGridAttributeSchema } from '@app/application/ProductGridAttribute/ProductGridAttribute.schema'

import { ProductGridAttribute } from '@app/domain/database/ProductGridAttribute'

export class Update {
  public async update(input: unknown): Promise<ProductGridAttributeResponse> {
    const dto = UpdateProductGridAttributeSchema.parse(input)

    const model = new ProductGridAttribute()

    // Check if attribute exists and get its productGridId
    const existingAttribute = await model.findById(dto.id)

    // Check if the name already exists for this product grid (if name is being updated)
    if (dto.name) {
      const duplicateAttribute = await model.findByNameAndProductGridId(
        dto.name,
        existingAttribute.productGridId,
      )

      if (duplicateAttribute && duplicateAttribute.id !== dto.id) {
        throw new ReferenceError(
          Language.translate('common:invalid', {
            name: 'product.grid.attribute.name',
          }),
        )
      }
    }

    return model.update(dto)
  }
}
