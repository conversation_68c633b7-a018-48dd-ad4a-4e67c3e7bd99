import { z } from 'zod'

import { PaginationFilterSchema } from '@thrift/common/engines/Pagination'

export const ProductGridAttributeBaseSchema = z.object({
  name: z.string().min(1).max(100),
})

export const CreateProductGridAttributeSchema =
  ProductGridAttributeBaseSchema.extend({
    productGridId: z.uuid(),
  })

export const UpdateProductGridAttributeSchema =
  ProductGridAttributeBaseSchema.extend({
    id: z.uuid(),
  }).partial({ name: true })

export const FilterProductGridAttributeSchema = PaginationFilterSchema.extend({
  productGridId: z.uuid(),
})
