import { Language } from '@app/application/Language'
import type { ProductGridAttributeResponse } from '@app/application/ProductGridAttribute/ProductGridAttribute'
import { CreateProductGridAttributeSchema } from '@app/application/ProductGridAttribute/ProductGridAttribute.schema'

import { ProductGrid } from '@app/domain/database/ProductGrid'
import { ProductGridAttribute } from '@app/domain/database/ProductGridAttribute'

export class Create {
  public async create(input: unknown): Promise<ProductGridAttributeResponse> {
    const dto = CreateProductGridAttributeSchema.parse(input)

    // Check if product grid exists
    const productGridModel = new ProductGrid()

    await productGridModel.findById(dto.productGridId)

    const model = new ProductGridAttribute()

    // Check if attribute name already exists for this product grid
    const existingAttribute = await model.findByNameAndProductGridId(
      dto.name,
      dto.productGridId,
    )

    if (existingAttribute) {
      throw new ReferenceError(
        Language.translate('common:invalid', {
          name: 'product.grid.attribute.name',
        }),
      )
    }

    return model.create(dto)
  }
}
