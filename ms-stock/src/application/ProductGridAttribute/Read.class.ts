import type { ProductGridAttributeWithVariationsResponse } from '@app/application/ProductGridAttribute/ProductGridAttribute'

import {
  type FetchProductGridAttributesProps,
  ProductGridAttribute,
} from '@app/domain/database/ProductGridAttribute'

export class Read {
  public async findById(
    id: string,
  ): Promise<ProductGridAttributeWithVariationsResponse> {
    const model = new ProductGridAttribute()

    return model.findById(id)
  }

  public async findByProductGridId(params: FetchProductGridAttributesProps) {
    const model = new ProductGridAttribute()

    return model.findByProductGridId(params)
  }
}
