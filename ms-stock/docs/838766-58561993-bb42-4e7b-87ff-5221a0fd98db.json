{"info": {"_postman_id": "838766-58561993-bb42-4e7b-87ff-5221a0fd98db", "name": "Stock", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "item": [{"name": "Stock", "item": [{"name": "/stocks/:id", "id": "838766-daaf2ae6-78fc-4f58-a830-04599c0f4777", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "OPTIONS", "header": [], "url": {"raw": "{{baseUrl}}/stocks/:id", "host": ["{{baseUrl}}"], "path": ["stocks", ":id"], "variable": [{"id": "903c90ae-ff39-4ac2-ad6a-3c45acd7523d", "key": "id", "value": ""}]}}, "response": []}, {"name": "/stocks", "id": "838766-b010cc23-6bc0-490c-8122-69df531f5e33", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "OPTIONS", "header": [], "url": {"raw": "{{baseUrl}}/stocks", "host": ["{{baseUrl}}"], "path": ["stocks"]}}, "response": []}, {"name": "/stocks/:id", "id": "838766-f9305e13-4f65-4c20-8966-e039dbb5c86e", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/stocks/:id", "host": ["{{baseUrl}}"], "path": ["stocks", ":id"], "variable": [{"id": "af3bb4d5-42bc-44ff-8c0e-e5ecf2c846b2", "key": "id", "value": ""}]}}, "response": []}, {"name": "/stocks", "id": "838766-66f43493-6bd5-4d99-a613-d450dcbc4c80", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/stocks?id={{id}}&page=1&perPage=10", "host": ["{{baseUrl}}"], "path": ["stocks"], "query": [{"key": "id", "value": "{{id}}"}, {"key": "page", "value": "1"}, {"key": "perPage", "value": "10"}]}}, "response": []}, {"name": "/stocks", "id": "838766-46877d40-4f10-40a2-b026-c3c20e557392", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\n    \"productId\": \"product-id\",\n    \"locationId\": \"location-id\",\n    \"quantity\": 100\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{baseUrl}}/stocks", "host": ["{{baseUrl}}"], "path": ["stocks"]}}, "response": []}, {"name": "/stocks/:id", "id": "838766-bfed54ef-2f72-4b40-a28a-92d22771a851", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "PUT", "header": [], "body": {"mode": "raw", "raw": "{\n    \"productId\": \"product-id\",\n    \"locationId\": \"location-id\",\n    \"quantity\": 150\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{baseUrl}}/stocks/:id", "host": ["{{baseUrl}}"], "path": ["stocks", ":id"], "variable": [{"id": "2ac76142-3630-47fd-9259-f8218027099f", "key": "id", "value": ""}]}}, "response": []}, {"name": "/stocks/:id", "id": "838766-d824bbc1-1661-4399-bf68-4e35d48164d0", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "DELETE", "header": [], "url": {"raw": "{{baseUrl}}/stocks/:id", "host": ["{{baseUrl}}"], "path": ["stocks", ":id"], "variable": [{"id": "852aa935-52a8-46de-95dc-02d1d54b3eda", "key": "id", "value": ""}]}}, "response": []}], "id": "838766-74aa6847-e6d2-460f-9336-ce66e3af3c5a"}, {"name": "Product", "item": [{"name": "/product/:id", "id": "838766-a7a68897-5670-41c8-becf-f5ff31421e87", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "OPTIONS", "header": [], "url": {"raw": "{{baseUrl}}/product/:id", "host": ["{{baseUrl}}"], "path": ["product", ":id"], "variable": [{"id": "d5efd1b8-05dd-4e77-a2f0-e678b2675661", "key": "id", "value": ""}]}}, "response": []}, {"name": "/product", "id": "838766-1dc001ac-8283-4821-81dd-780ee515f596", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "OPTIONS", "header": [], "url": {"raw": "{{baseUrl}}/product", "host": ["{{baseUrl}}"], "path": ["product"]}}, "response": []}, {"name": "/product/:id", "id": "838766-15176b04-0ccc-4861-b41a-226bef715e78", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/product/:id", "host": ["{{baseUrl}}"], "path": ["product", ":id"], "variable": [{"id": "ca33d16c-7d19-4808-85be-0c43cdcfa349", "key": "id", "value": ""}]}}, "response": []}, {"name": "/product", "id": "838766-9d197c1b-61ea-4456-9102-432fb9127804", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/product?page=1&perPage=10", "host": ["{{baseUrl}}"], "path": ["product"], "query": [{"key": "page", "value": "1"}, {"key": "perPage", "value": "10"}]}}, "response": []}, {"name": "/product", "id": "838766-592200d4-136a-4247-883e-5590ee910266", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\n    \"name\": \"Example Product\",\n    \"description\": \"Product description\",\n    \"categoryId\": \"category-id\",\n    \"sku\": \"PROD-001\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{baseUrl}}/product", "host": ["{{baseUrl}}"], "path": ["product"]}}, "response": []}, {"name": "/product/:id", "id": "838766-6b9f7a83-c0ce-4b8a-9e86-054c215172ee", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "PUT", "header": [], "body": {"mode": "raw", "raw": "{\n    \"name\": \"Updated Product\",\n    \"description\": \"Updated description\",\n    \"categoryId\": \"category-id\",\n    \"sku\": \"PROD-001\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{baseUrl}}/product/:id", "host": ["{{baseUrl}}"], "path": ["product", ":id"], "variable": [{"id": "8b5a8278-e938-446a-911f-293bd77074de", "key": "id", "value": ""}]}}, "response": []}, {"name": "/product/:id", "id": "838766-2c504c94-4998-4b64-afd2-e0ae2c4468d8", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "DELETE", "header": [], "url": {"raw": "{{baseUrl}}/product/:id", "host": ["{{baseUrl}}"], "path": ["product", ":id"], "variable": [{"id": "e41d30f4-041b-4fac-8c18-87fa18063fac", "key": "id", "value": ""}]}}, "response": []}], "id": "838766-a0bfbba9-3616-4e81-9cf4-a80c84c3617b"}, {"name": "Tag", "item": [{"name": "/tags/:id", "id": "838766-05a15f71-182e-4d58-b4e6-192e2c8a12cd", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "OPTIONS", "header": [], "url": {"raw": "{{baseUrl}}/tags/:id", "host": ["{{baseUrl}}"], "path": ["tags", ":id"], "variable": [{"key": "id", "value": ""}]}}, "response": []}, {"name": "/tags", "id": "838766-b24c5e50-4935-41fe-bb13-546fd199b3ce", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "OPTIONS", "header": [], "url": {"raw": "{{baseUrl}}/tags", "host": ["{{baseUrl}}"], "path": ["tags"]}}, "response": []}, {"name": "/tags/:id", "id": "838766-53eaac11-07dc-4a35-b83e-eb963c54e7cc", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/tags/:id", "host": ["{{baseUrl}}"], "path": ["tags", ":id"], "variable": [{"key": "id", "value": ""}]}}, "response": []}, {"name": "/tags", "id": "838766-820ce9d9-f00a-44da-bffd-66b523950a29", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/tags?page=1&perPage=10", "host": ["{{baseUrl}}"], "path": ["tags"], "query": [{"key": "page", "value": "1"}, {"key": "perPage", "value": "10"}]}}, "response": []}, {"name": "/tags", "id": "838766-e3e7e663-be6a-4c78-adff-4d35c8d6715c", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\n    \"name\": \"<PERSON><PERSON><PERSON>\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{baseUrl}}/tags", "host": ["{{baseUrl}}"], "path": ["tags"]}}, "response": []}, {"name": "/tags/:id", "id": "838766-d64d3755-0a70-4992-94e1-528c2967c461", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "PUT", "header": [], "body": {"mode": "raw", "raw": "{\n    \"name\": \"<PERSON><PERSON>\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{baseUrl}}/tags/:id", "host": ["{{baseUrl}}"], "path": ["tags", ":id"], "variable": [{"key": "id", "value": "e8d4bca7-4ed9-4597-870d-d0ae3ed81967"}]}}, "response": []}, {"name": "/tags/:id", "id": "838766-a9406a8c-5f06-4406-830c-c3abf53e295c", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "DELETE", "header": [], "url": {"raw": "{{baseUrl}}/tags/:id", "host": ["{{baseUrl}}"], "path": ["tags", ":id"], "variable": [{"key": "id", "value": ""}]}}, "response": []}], "id": "838766-ba9c1e44-8483-4e4f-ad9e-be48a969d35c"}, {"name": "Product Category", "item": [{"name": "/product-categories/:id", "id": "838766-0b917dec-7d4c-4ab3-8ffe-09536e4dc376", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "OPTIONS", "header": [], "url": {"raw": "{{baseUrl}}/product-categories/:id", "host": ["{{baseUrl}}"], "path": ["product-categories", ":id"], "variable": [{"id": "2fb25d69-241c-4fab-ab1d-b6810fc37720", "key": "id", "value": ""}]}}, "response": []}, {"name": "/product-categories", "id": "838766-995b7494-ed6e-49f6-bd09-40701014c822", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "OPTIONS", "header": [], "url": {"raw": "{{baseUrl}}/product-categories", "host": ["{{baseUrl}}"], "path": ["product-categories"]}}, "response": []}, {"name": "/product-categories/:id", "id": "838766-570d080f-0b20-4527-b8b7-cf23c7d163ff", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/product-categories/:id", "host": ["{{baseUrl}}"], "path": ["product-categories", ":id"], "variable": [{"id": "0dd68a4f-5894-4437-951c-8b8e6269451d", "key": "id", "value": ""}]}}, "response": []}, {"name": "/product-categories", "id": "838766-ffa2fac4-d002-4916-9539-cca3294129bd", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/product-categories?page=1&perPage=10", "host": ["{{baseUrl}}"], "path": ["product-categories"], "query": [{"key": "page", "value": "1"}, {"key": "perPage", "value": "10"}]}}, "response": []}, {"name": "/product-categories", "id": "838766-a8c3a594-4e84-4e97-af29-787ad4be21b9", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\n    \"name\": \"Example Category\",\n    \"description\": \"Category description\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{baseUrl}}/product-categories", "host": ["{{baseUrl}}"], "path": ["product-categories"]}}, "response": []}, {"name": "/product-categories/:id", "id": "838766-d84f778e-6227-4ad4-b96c-2baf8e2346f2", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "PUT", "header": [], "body": {"mode": "raw", "raw": "{\n    \"name\": \"Updated Category\",\n    \"description\": \"Updated description\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{baseUrl}}/product-categories/:id", "host": ["{{baseUrl}}"], "path": ["product-categories", ":id"], "variable": [{"id": "a2928260-8a72-40a1-a292-fd71edf344c1", "key": "id", "value": ""}]}}, "response": []}, {"name": "/product-categories/:id", "id": "838766-e1d3ee6f-e8e1-4ed8-a27e-250a1aeeb05b", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "DELETE", "header": [], "url": {"raw": "{{baseUrl}}/product-categories/:id", "host": ["{{baseUrl}}"], "path": ["product-categories", ":id"], "variable": [{"id": "77241062-9ffc-4f56-a70e-51250547b29d", "key": "id", "value": ""}]}}, "response": []}], "id": "838766-21361304-a6c7-493d-af6f-897c20344080"}, {"name": "Stock Location", "item": [{"name": "/stock-locations/:id", "id": "838766-e6b878be-896b-4cd1-8902-f6b7952336d1", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "OPTIONS", "header": [], "url": {"raw": "{{baseUrl}}/stock-locations/:id", "host": ["{{baseUrl}}"], "path": ["stock-locations", ":id"], "variable": [{"id": "714372ff-26f8-4d7c-b8d1-b28ec8e35c7d", "key": "id", "value": ""}]}}, "response": []}, {"name": "/stock-locations", "id": "838766-9695973b-3368-459f-b9f7-1b8a8dfa385d", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "OPTIONS", "header": [], "url": {"raw": "{{baseUrl}}/stock-locations", "host": ["{{baseUrl}}"], "path": ["stock-locations"]}}, "response": []}, {"name": "/stock-locations/:id", "id": "838766-f381632a-082b-4f97-90cc-00da2359e167", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/stock-locations/:id", "host": ["{{baseUrl}}"], "path": ["stock-locations", ":id"], "variable": [{"id": "5ec0f8fc-f19c-4e1c-af83-74b3ac0acc42", "key": "id", "value": ""}]}}, "response": []}, {"name": "/stock-locations", "id": "838766-3e32f29a-f164-4630-8455-b4ce043e54f9", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/stock-locations?page=1&perPage=10", "host": ["{{baseUrl}}"], "path": ["stock-locations"], "query": [{"key": "page", "value": "1"}, {"key": "perPage", "value": "10"}]}}, "response": []}, {"name": "/stock-locations", "id": "838766-7fc84b7d-cdbc-4b1d-b85e-d6185beed5a9", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\n    \"name\": \"Example Location\",\n    \"address\": \"123 Warehouse St\",\n    \"type\": \"warehouse\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{baseUrl}}/stock-locations", "host": ["{{baseUrl}}"], "path": ["stock-locations"]}}, "response": []}, {"name": "/stock-locations/:id", "id": "838766-e54df697-2586-4382-8d80-22113939de96", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "PUT", "header": [], "body": {"mode": "raw", "raw": "{\n    \"name\": \"Updated Location\",\n    \"address\": \"456 Storage Ave\",\n    \"type\": \"warehouse\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{baseUrl}}/stock-locations/:id", "host": ["{{baseUrl}}"], "path": ["stock-locations", ":id"], "variable": [{"id": "c44fa185-5381-426c-960e-3dfd9ba345da", "key": "id", "value": ""}]}}, "response": []}, {"name": "/stock-locations/:id", "id": "838766-48b1a8be-7d9e-4fd4-be64-6946bc7083a3", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "DELETE", "header": [], "url": {"raw": "{{baseUrl}}/stock-locations/:id", "host": ["{{baseUrl}}"], "path": ["stock-locations", ":id"], "variable": [{"id": "86e780c7-fdbf-479c-b105-458c8366744e", "key": "id", "value": ""}]}}, "response": []}], "id": "838766-9ce8c20c-c85f-4767-a5c5-076c7c536b2d"}], "auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{token}}", "type": "string"}]}, "event": [{"listen": "prerequest", "script": {"id": "f9ab3974-15be-4344-be71-7b82fce328ba", "type": "text/javascript", "packages": {}, "exec": [""]}}, {"listen": "test", "script": {"id": "1601a4fb-c9e5-46e6-87cd-c15369916bc1", "type": "text/javascript", "packages": {}, "exec": [""]}}], "variable": [{"key": "baseUrl", "value": "{{defaultBaseUrl}}/stock", "type": "string"}]}