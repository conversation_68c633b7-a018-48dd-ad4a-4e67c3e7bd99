{"info": {"_postman_id": "838766-80f77b01-d4aa-426d-a38f-5fcfaf4c5356", "name": "Evaluation", "description": "API collection for the Evaluation microservice - manages evaluation process of used products submitted by suppliers", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "item": [{"name": "Evaluation Batches", "item": [{"name": "/evaluation-batches", "id": "838766-07a2a4d9-929c-4174-af2a-bb50a49f0b42", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"supplierId\": \"supplier-001-uuid-sample\", // Person ID - Fornecedor\n  \"storeId\": \"store-001-uuid-sample\", // Loja\n  \"status\": \"PENDING_REVIEW\", \n  /**\n    'PENDING_REVIEW',\n    'UNDER_EVALUATION',\n    'EVALUATION_COMPLETED',\n    'PROPOSAL_SENT',\n    'PROPOSAL_ACCEPTED',\n    'PROPOSAL_REJECTED',\n    'CLOSED',\n  **/\n  \"displayCode\": \"EB004\", // Codigo que irá exibir para senha do lote na tela de espera\n  \"notes\": \"New evaluation batch for testing\"\n}"}, "url": {"raw": "{{baseUrl}}/evaluation-batches", "host": ["{{baseUrl}}"], "path": ["evaluation-batches"]}}, "response": []}, {"name": "/evaluation-batches", "id": "838766-87d126ab-65d8-4299-a358-09e14fe74258", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/evaluation-batches?page=1&perPage=10", "host": ["{{baseUrl}}"], "path": ["evaluation-batches"], "query": [{"key": "page", "value": "1"}, {"key": "perPage", "value": "10"}, {"key": "supplierId", "value": "", "disabled": true}, {"key": "storeId", "value": "", "disabled": true}, {"key": "status", "value": "", "disabled": true}, {"key": "search", "value": "", "disabled": true}]}}, "response": []}, {"name": "/evaluation-batches/:id", "id": "838766-21625c4a-2e05-4cec-9215-b826c0a38a6d", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/evaluation-batches/:id", "host": ["{{baseUrl}}"], "path": ["evaluation-batches", ":id"], "variable": [{"id": "cd431bef-bb37-452f-a938-47904563d427", "key": "id", "value": "eb-001-uuid-sample-batch-001"}]}}, "response": []}, {"name": "/evaluation-batches/:id", "id": "838766-f8ccb109-224d-445a-be0f-0294a43e0743", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"status\": \"UNDER_EVALUATION\",\n  \"notes\": \"Updated notes for evaluation batch\"\n}"}, "url": {"raw": "{{baseUrl}}/evaluation-batches/:id", "host": ["{{baseUrl}}"], "path": ["evaluation-batches", ":id"], "variable": [{"id": "d34b175e-2acd-463d-ac06-23a44cddd043", "key": "id", "value": "eb-001-uuid-sample-batch-001"}]}}, "response": []}, {"name": "/evaluation-batches/:id", "id": "838766-74cd8239-4690-410b-bac5-20df6ff89ab0", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "DELETE", "header": [], "url": {"raw": "{{baseUrl}}/evaluation-batches/:id", "host": ["{{baseUrl}}"], "path": ["evaluation-batches", ":id"], "variable": [{"id": "db9c4dd3-bb8f-4432-849c-62cad21ee47d", "key": "id", "value": "eb-001-uuid-sample-batch-001"}]}}, "response": []}], "id": "838766-d93c69f0-290b-4228-810b-6bba6c9746e4"}, {"name": "Evaluation Items", "item": [{"name": "/evaluation-batches/:batchId/items", "id": "838766-41b8014e-8479-472d-ad06-301771b23c48", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"supplierDescription\": \"Vintage denim jacket\", \n  \"supplierSuggestedCategoryId\": \"cat-clothing-001\", // Id da categoria do produto sugerido\n  \"evaluatedProductCategoryId\": \"cat-clothing-001\", // Id da categoria do produto que o lojista irá colocar\n  \"evaluatedProductClassificationId\": \"class-vintage-001\", // Id da classificação do produto\n  \"evaluatorNotes\": \"Good condition, authentic vintage piece\", // Notas do avaliador\n  \"proposedBuyPrice\": 55.00, // Proposta estimada do item\n  \"status\": \"PENDING_EVALUATION\", // Status do item dentro do lote\n  \"estimatedMarketValue\": 150.00 // Valor do item no mercado como novo\n}"}, "url": {"raw": "{{baseUrl}}/evaluation-batches/:batchId/items", "host": ["{{baseUrl}}"], "path": ["evaluation-batches", ":batchId", "items"], "variable": [{"key": "batchId", "value": "eb-001-uuid-sample-batch-001"}]}}, "response": []}, {"name": "/evaluation-items", "id": "838766-2c1731a0-7070-495b-a0f0-decad5b361e1", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/evaluation-items?page=1&perPage=10", "host": ["{{baseUrl}}"], "path": ["evaluation-items"], "query": [{"key": "page", "value": "1"}, {"key": "perPage", "value": "10"}, {"key": "evaluationBatchId", "value": "", "disabled": true}, {"key": "status", "value": "", "disabled": true}]}}, "response": []}, {"name": "/evaluation-items/:id", "id": "838766-38296d6e-dbb1-439c-b9f3-2544d9b5f5b4", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/evaluation-items/:id", "host": ["{{baseUrl}}"], "path": ["evaluation-items", ":id"], "variable": [{"id": "e81cb5f5-8b19-4b45-ba1a-069baf8ed788", "key": "id", "value": "ei-001-uuid-sample-item-001"}]}}, "response": []}, {"name": "/evaluation-items/:id", "id": "838766-1b62d284-195c-4c22-b3eb-42b3b3772863", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"status\": \"ACCEPTED\",\n  \"evaluatorNotes\": \"Excellent condition, high resale value\",\n  \"proposedBuyPrice\": 60.00\n}"}, "url": {"raw": "{{baseUrl}}/evaluation-items/:id", "host": ["{{baseUrl}}"], "path": ["evaluation-items", ":id"], "variable": [{"id": "e31abaf6-4c50-44f5-966f-f89bdfe76944", "key": "id", "value": "ei-001-uuid-sample-item-001"}]}}, "response": []}, {"name": "/evaluation-items/:id", "id": "838766-c3c3615b-1f8f-4d20-af41-f1a89a785fbe", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "DELETE", "header": [], "url": {"raw": "{{baseUrl}}/evaluation-items/:id", "host": ["{{baseUrl}}"], "path": ["evaluation-items", ":id"], "variable": [{"id": "*************-4351-bd3d-f99ff2bc2b57", "key": "id", "value": "ei-001-uuid-sample-item-001"}]}}, "response": []}], "id": "838766-59b24244-929f-4225-8aee-4d4669c5ff6c"}, {"name": "Evaluation Proposals", "item": [{"name": "/evaluation-proposals", "id": "838766-ac03f037-cc2e-48d6-b41c-582b52b90e56", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"evaluationBatchId\": \"eb-001-uuid-sample-batch-001\",\n  \"totalProposedValue\": 130.00,\n  \"status\": \"DRAFT\"\n}"}, "url": {"raw": "{{baseUrl}}/evaluation-proposals", "host": ["{{baseUrl}}"], "path": ["evaluation-proposals"]}}, "response": []}, {"name": "/evaluation-proposals", "id": "838766-c25a2e2e-1a79-4b9d-8810-a5911ab74baa", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/evaluation-proposals?page=1&perPage=10", "host": ["{{baseUrl}}"], "path": ["evaluation-proposals"], "query": [{"key": "page", "value": "1"}, {"key": "perPage", "value": "10"}, {"key": "evaluationBatchId", "value": "", "disabled": true}, {"key": "status", "value": "", "disabled": true}]}}, "response": []}, {"name": "/evaluation-proposals/:id", "id": "838766-e03d907f-9c8c-4533-be90-1a84d46826db", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/evaluation-proposals/:id", "host": ["{{baseUrl}}"], "path": ["evaluation-proposals", ":id"], "variable": [{"id": "eb9adf1c-06da-47ec-bb5a-3b148a162744", "key": "id", "value": "ep-001-uuid-sample-proposal-001"}]}}, "response": []}, {"name": "/evaluation-proposals/:id/send", "id": "838766-0fde49e8-fd99-4e0a-b87c-179701580d7f", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "PUT", "header": [], "url": {"raw": "{{baseUrl}}/evaluation-proposals/:id/send", "host": ["{{baseUrl}}"], "path": ["evaluation-proposals", ":id", "send"], "variable": [{"id": "11c331e5-cc34-4917-a0f6-985207efbeed", "key": "id", "value": "ep-001-uuid-sample-proposal-001"}]}}, "response": []}, {"name": "/evaluation-proposals/:id/respond", "id": "838766-ca319eca-f9db-4561-a59a-3e8b2c2db78f", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"status\": \"ACCEPTED_BY_SUPPLIER\",\n  \"supplierNotes\": \"Agreed to all proposed values. Ready for pickup.\"\n}"}, "url": {"raw": "{{baseUrl}}/evaluation-proposals/:id/respond", "host": ["{{baseUrl}}"], "path": ["evaluation-proposals", ":id", "respond"], "variable": [{"id": "b1450d9d-64c5-41b1-880f-861d181da024", "key": "id", "value": "ep-001-uuid-sample-proposal-001"}]}}, "response": []}], "id": "838766-79a87e26-b410-4f06-935a-208fb801216b"}], "auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{token}}", "type": "string"}]}, "event": [{"listen": "prerequest", "script": {"id": "2c0a7b41-949a-4e34-8e58-4d20c8d2f131", "type": "text/javascript", "packages": {}, "exec": [""]}}, {"listen": "test", "script": {"id": "84fd40cd-55ee-4082-9c04-7b27f80463b0", "type": "text/javascript", "packages": {}, "exec": [""]}}], "variable": [{"key": "baseUrl", "value": "{{defaultBaseUrl}}/evaluation", "type": "string"}]}